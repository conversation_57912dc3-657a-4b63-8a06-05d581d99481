use anyhow::{Context, Result};
use chrono::Local;
use clap::Parser;
use egui_plot::PlotPoint;
use gethostname::gethostname;
use gui::LabrecordApp;
use serde::Deserialize;
use std::fs;
use std::io::{stdout, Write};
use std::path::{Path, PathBuf};
use std::sync::atomic::AtomicBool;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::fs::File;
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWriteExt, BufWriter};
use tokio::sync::Mutex;
use tokio_serial;

mod gui;

// Constants
const BURST_SAMPLES: usize = 16; // Number of samples per packet
const SAMPLES_PER_SECOND: usize = 800;
const TIME_PER_SAMPLE: Duration = Duration::from_millis(1000 / SAMPLES_PER_SECOND as u64);
const PACKET_SIZE: usize = 108; // Total size of UART packet

#[derive(Parser, Debug)]
#[command(author, version, about = "Record ADXL345 accelerometer data")]
struct Cli {}

#[derive(Debug, Deserialize)]
pub struct Config {
    port: String,
    scenario: String,
}

/// A sample from the ADXL345 accelerometer
#[derive(Debug, Clone, Copy)]
struct Sample {
    x: i16,
    y: i16,
    z: i16,
}

/// Binary UART packet structure matching the firmware
/// We assume little endian everywhere :D
#[repr(C, packed)]
#[derive(Debug)]
struct UartPacket {
    header: [u8; 4],                  // "ADXL" magic header
    sensor_id: u8,                    // Sensor ID (1 or 2)
    timestamp: u32,                   // Timestamp
    sample_count: u8,                 // Number of samples (should be 16)
    accel: [[i16; 3]; BURST_SAMPLES], // Raw accelerometer data
    checksum: u16,                    // Simple checksum for data integrity
}

/// A simple CSV file logger
struct CsvLogger {
    file: BufWriter<File>,
    buffer: String,
}

impl CsvLogger {
    /// Create a new CSV logger
    async fn new(path: &Path) -> Result<Self> {
        // Ensure the directory exists
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent).context("Failed to create directory")?;
        }

        // Create the file
        let mut file = File::create(path)
            .await
            .context("Failed to create CSV file")?;

        // Write the header
        file.write_all(b"time_offset,x1,y1,z1,x2,y2,z2\n")
            .await
            .context("Failed to write CSV header")?;
        let file = BufWriter::new(file);

        Ok(Self {
            file,
            buffer: String::new(),
        })
    }

    /// Log a sample to the CSV file
    async fn log(
        &mut self,
        time_offset: Duration,
        sample1: &Sample,
        sample2: &Sample,
    ) -> Result<()> {
        use std::fmt::Write;

        self.buffer.clear();
        write!(
            &mut self.buffer,
            "{},{},{},{},{},{},{}\n",
            time_offset.as_millis(),
            sample1.x,
            sample1.y,
            sample1.z,
            sample2.x,
            sample2.y,
            sample2.z,
        )
        .context("Failed to format sample")?;

        self.file
            .write_all(self.buffer.as_bytes())
            .await
            .context("Failed to write sample to CSV file")?;

        Ok(())
    }

    /// Flush the CSV writer
    async fn flush(&mut self) -> Result<()> {
        self.file
            .flush()
            .await
            .context("Failed to flush CSV file")?;
        Ok(())
    }
}

/// Calculate checksum for packet data (excluding checksum field itself)
fn calculate_checksum(data: &[u8]) -> u16 {
    data.iter().map(|&b| b as u16).sum()
}

/// Parse a UART packet from raw bytes
fn parse_uart_packet(buffer: &[u8]) -> Result<UartPacket> {
    if buffer.len() != PACKET_SIZE {
        anyhow::bail!(
            "Invalid packet size: expected {}, got {}",
            PACKET_SIZE,
            buffer.len()
        );
    }

    // Check header
    if &buffer[0..4] != b"ADXL" {
        anyhow::bail!(
            "Invalid packet header: expected 'ADXL', got {:02X?} ('{}')",
            &buffer[0..4],
            String::from_utf8_lossy(&buffer[0..4])
        );
    }

    let sensor_id = buffer[4];
    let timestamp = u32::from_le_bytes([buffer[5], buffer[6], buffer[7], buffer[8]]);
    let sample_count = buffer[9];

    if sample_count == 0 || sample_count > BURST_SAMPLES as u8 {
        anyhow::bail!(
            "Invalid sample count: expected 1-{}, got {}",
            BURST_SAMPLES,
            sample_count
        );
    }

    // Parse accelerometer data (actual sample count × 3 axes × 2 bytes per int16)
    let mut accel = [[0i16; 3]; BURST_SAMPLES];
    for i in 0..(sample_count as usize) {
        let base_idx = 10 + i * 6; // Start after header fields
        if base_idx + 5 < buffer.len() {
            accel[i][0] = i16::from_le_bytes([buffer[base_idx], buffer[base_idx + 1]]);
            accel[i][1] = i16::from_le_bytes([buffer[base_idx + 2], buffer[base_idx + 3]]);
            accel[i][2] = i16::from_le_bytes([buffer[base_idx + 4], buffer[base_idx + 5]]);
        }
    }

    let checksum = u16::from_le_bytes([buffer[106], buffer[107]]);

    // Verify checksum
    let calculated_checksum = calculate_checksum(&buffer[0..106]);
    if checksum != calculated_checksum {
        anyhow::bail!(
            "Checksum mismatch: expected {}, got {}",
            calculated_checksum,
            checksum
        );
    }

    Ok(UartPacket {
        header: [buffer[0], buffer[1], buffer[2], buffer[3]],
        sensor_id,
        timestamp,
        sample_count,
        accel,
        checksum,
    })
}

/// Convert UartPacket accelerometer data to Sample vector
fn packet_to_samples(packet: &UartPacket) -> Vec<Sample> {
    let sample_count = packet.sample_count as usize;
    let mut samples = Vec::with_capacity(sample_count);
    for i in 0..sample_count {
        // Copy values to avoid packed struct alignment issues
        let x = packet.accel[i][0];
        let y = packet.accel[i][1];
        let z = packet.accel[i][2];
        samples.push(Sample { x, y, z });
    }
    samples
}

fn main() {
    // Set up Ctrl+C handler
    let running = Arc::new(std::sync::atomic::AtomicBool::new(true));
    let r = running.clone();
    ctrlc::set_handler(move || {
        eprintln!("\nReceived Ctrl+C, shutting down...");
        r.store(false, std::sync::atomic::Ordering::SeqCst);
        std::thread::sleep(Duration::from_millis(1500));
        std::process::exit(0);
    })
    .context("Error setting Ctrl+C handler")
    .unwrap();

    let gui = gui::LabrecordApp::default();

    let gui2 = gui.clone();
    let running2 = running.clone();

    std::thread::Builder::new()
        .name("data-recorder".to_string())
        .spawn(move || {
            let rt = tokio::runtime::Runtime::new()
                .context("Failed to create Tokio runtime")
                .unwrap();
            rt.block_on(app_loop(gui2, running2));
        })
        .unwrap();

    gui::run_gui(gui);

    running.store(false, std::sync::atomic::Ordering::SeqCst);
}

async fn app_loop(gui: LabrecordApp, running: Arc<AtomicBool>) {
    while let Err(e) = app(gui.clone(), running.clone()).await {
        eprintln!("Error in main app: {:?}", e);
    }
}

async fn app(gui: LabrecordApp, running: Arc<AtomicBool>) -> Result<()> {
    let _args = Cli::parse();
    let config = fs::read_to_string("config.toml").context("Failed to read config file")?;
    let config: Config = toml::from_str(&config).context("Failed to parse config file")?;

    let mut stdout = stdout().lock();

    let scenario_start = Local::now();
    let scenario_start = scenario_start.format("%Y-%m-%d_%H-%M-%S").to_string();

    let mut last_batch_start = Instant::now() - Duration::from_secs(10);

    'outer: while running.load(std::sync::atomic::Ordering::SeqCst) {
        // Connect to the serial port
        let stream: Box<dyn AsyncRead + Unpin + Send> = {
            writeln!(stdout, "Opening serial port {} at 460800 baud", config.port)?;
            Box::new(
                match tokio_serial::SerialStream::open(&tokio_serial::new(&config.port, 460_800))
                    .context("Failed to open serial port")
                {
                    Ok(stream) => stream,
                    Err(e) => {
                        writeln!(stdout, "Error opening serial port: {}", e)?;
                        tokio::time::sleep(Duration::from_secs(2)).await;
                        writeln!(stdout, "Retrying...")?;
                        continue 'outer;
                    }
                },
            )
        };
        let mut stream = tokio::io::BufReader::new(stream);
        writeln!(stdout, "Connected!")?;

        // Give the firmware some time to stabilize before starting data collection
        writeln!(stdout, "Waiting 2 seconds for firmware to stabilize...")?;
        tokio::time::sleep(Duration::from_secs(2)).await;

        // Synchronize to packet boundary by finding "ADXL" header
        writeln!(stdout, "Synchronizing to packet boundary...")?;
        let mut sync_byte = [0u8; 1];
        let mut header_buffer = [0u8; 4];
        let mut sync_attempts = 0;

        // Read bytes one by one until we find "ADXL"
        loop {
            stream.read_exact(&mut sync_byte).await?;

            // Shift the header buffer and add the new byte
            header_buffer[0] = header_buffer[1];
            header_buffer[1] = header_buffer[2];
            header_buffer[2] = header_buffer[3];
            header_buffer[3] = sync_byte[0];

            sync_attempts += 1;
            if sync_attempts > 1000 {
                writeln!(stdout, "Failed to find ADXL header after 1000 attempts")?;
                writeln!(
                    stdout,
                    "Last 4 bytes received: {:02X?} ('{}')",
                    &header_buffer,
                    String::from_utf8_lossy(&header_buffer)
                )?;
                continue 'outer;
            }

            // Debug: Print every 100 bytes during sync
            if sync_attempts % 100 == 0 {
                writeln!(
                    stdout,
                    "Sync attempt {}: looking for ADXL, got {:02X?} ('{}')",
                    sync_attempts,
                    &header_buffer,
                    String::from_utf8_lossy(&header_buffer)
                )?;
            }

            if &header_buffer == b"ADXL" {
                writeln!(stdout, "Found ADXL header after {} bytes", sync_attempts)?;
                break;
            }
        }

        // We found "ADXL", now we need to read the rest of this packet to get to the boundary
        let mut remainder = vec![0u8; PACKET_SIZE - 4];
        stream
            .read_exact(&mut remainder)
            .await
            .context("Failed to read packet remainder")?;

        writeln!(stdout, "Synchronized to packet boundary")?;

        while running.load(std::sync::atomic::Ordering::SeqCst) {
            // Create the CSV logger
            // ensure that we do not reuse the same filename
            tokio::time::sleep_until((last_batch_start + Duration::from_secs(1)).into()).await;
            let batch_start = Local::now();
            let batch_start = batch_start.format("%Y-%m-%d_%H-%M-%S").to_string();
            last_batch_start = Instant::now();

            let output_path = PathBuf::from(format!("readings/readings-{}.csv", batch_start));
            println!("Logging to {}", output_path.display());
            tokio::fs::write(
                output_path.with_extension("json"),
                serde_json::json!({
                    "scenario": config.scenario,
                    "batch_start": batch_start,
                    "scenario_start": scenario_start,
                    "hostname": gethostname().to_string_lossy(),
                    "source": config.port,
                })
                .to_string(),
            )
            .await
            .context("Failed to write metadata JSON")?;
            let mut csv_logger = CsvLogger::new(&output_path).await?;

            if let Err(e) = do_batch(&mut stream, &mut csv_logger, &gui).await {
                writeln!(stdout, "Error while collecting batch: {}", e)?;
                let _ = csv_logger.flush().await;
                continue 'outer;
            }

            let _ = csv_logger.flush().await;
            writeln!(stdout, "Batch complete")?;
        }
    }

    writeln!(stdout, "Shutting down...")?;

    Ok(())
}

async fn add_samples(sensor: &Arc<Mutex<gui::SensorData>>, time_offset: Duration, sample: &Sample) {
    let mut sensor = sensor.lock().await;
    sensor.x.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.x as f64,
    });
    sensor.y.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.y as f64,
    });
    sensor.z.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.z as f64,
    });
}

async fn do_batch(
    stream: &mut (dyn AsyncRead + Unpin),
    csv_logger: &mut CsvLogger,
    gui: &LabrecordApp,
) -> Result<()> {
    let batch_start = Instant::now() - TIME_PER_SAMPLE * BURST_SAMPLES as u32;

    let mut packet_buffer = vec![0u8; PACKET_SIZE];
    let mut sensor1_samples = Vec::new();
    let mut sensor2_samples = Vec::new();

    // Process packets for one hour (approximately)
    let packets_per_hour = (SAMPLES_PER_SECOND * 60 * 60) / BURST_SAMPLES;

    for packet_num in 0..packets_per_hour {
        // Read one complete packet
        stream
            .read_exact(&mut packet_buffer)
            .await
            .context("Failed to read packet")?;

        // Debug: Print first 20 bytes of every 100th packet to see what we're getting
        if packet_num % 100 == 0 {
            println!(
                "Raw packet {} data: {:02X?}",
                packet_num,
                &packet_buffer[0..20.min(packet_buffer.len())]
            );
        }

        // Parse the packet
        let packet = match parse_uart_packet(&packet_buffer) {
            Ok(p) => p,
            Err(e) => {
                println!("Failed to parse packet {}: {}", packet_num, e);
                // Try to resynchronize
                continue;
            }
        };

        // Convert packet to samples
        let samples = packet_to_samples(&packet);

        // Debug: Print packet info occasionally (copy values to avoid packed struct issues)
        if packet_num % 100 == 0 {
            let sensor_id = packet.sensor_id;
            let sample_count = packet.sample_count;
            let timestamp = packet.timestamp;
            println!(
                "Packet {}: Sensor {} with {} samples at timestamp {}",
                packet_num, sensor_id, sample_count, timestamp
            );
        }

        // Store samples and process immediately based on sensor ID
        let sensor_id = packet.sensor_id;
        let now = Instant::now();

        match sensor_id {
            1 => {
                // Process Sensor 1 samples immediately
                for (i, sample) in samples.iter().enumerate() {
                    let time = now - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
                    let time_offset = time - batch_start;

                    // Update GUI immediately
                    add_samples(&gui.sensor1, time_offset, sample).await;
                }
                sensor1_samples = samples;
            }
            2 => {
                // Process Sensor 2 samples immediately
                for (i, sample) in samples.iter().enumerate() {
                    let time = now - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
                    let time_offset = time - batch_start;

                    // Update GUI immediately
                    add_samples(&gui.sensor2, time_offset, sample).await;
                }
                sensor2_samples = samples;
            }
            _ => {
                println!("Warning: Invalid sensor ID: {}", sensor_id);
                continue;
            }
        }

        // Log to CSV when we have samples from both sensors (for dual-sensor CSV format)
        if !sensor1_samples.is_empty() && !sensor2_samples.is_empty() {
            // Log all samples from this burst
            for i in 0..BURST_SAMPLES
                .min(sensor1_samples.len())
                .min(sensor2_samples.len())
            {
                let time = now - (BURST_SAMPLES - i) as u32 * TIME_PER_SAMPLE;
                let time_offset = time - batch_start;

                csv_logger
                    .log(time_offset, &sensor1_samples[i], &sensor2_samples[i])
                    .await
                    .context("Failed to log sample")?;
            }

            // Clear the samples after logging to CSV
            sensor1_samples.clear();
            sensor2_samples.clear();
        }

        // Print progress occasionally
        if packet_num % 1000 == 0 {
            println!("Processed {} packets", packet_num);
        }
    }

    Ok(())
}
