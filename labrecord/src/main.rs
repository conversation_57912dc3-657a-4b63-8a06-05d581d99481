use anyhow::{Context, Result};
use chrono::Local;
use clap::Parser;
use egui_plot::PlotPoint;
use gethostname::gethostname;
use gui::LabrecordApp;
use serde::Deserialize;
use std::fs;
use std::io::{stdout, Write};
use std::path::{Path, PathBuf};
use std::sync::atomic::AtomicBool;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::fs::File;
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWriteExt, BufWriter};
use tokio::sync::Mutex;
use tokio_serial;

mod gui;

// Constants
const FIFO_SAMPLE_SIZE: usize = 6; // x, y, z as i16 (2 bytes each)
const SAMPLES_PER_SECOND: usize = 800;
const TIME_PER_SAMPLE: Duration = Duration::from_millis(1000 / SAMPLES_PER_SECOND as u64);

#[derive(Parser, Debug)]
#[command(author, version, about = "Record ADXL345 accelerometer data")]
struct Cli {}

#[derive(Debug, Deserialize)]
pub struct Config {
    port: String,
    scenario: String,
}

/// A sample from the ADXL345 accelerometer
#[derive(Debug, Clone, Copy)]
struct Sample {
    x: i16,
    y: i16,
    z: i16,
}

/// A simple CSV file logger
struct CsvLogger {
    file: BufWriter<File>,
    buffer: String,
}

impl CsvLogger {
    /// Create a new CSV logger
    async fn new(path: &Path) -> Result<Self> {
        // Ensure the directory exists
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent).context("Failed to create directory")?;
        }

        // Create the file
        let mut file = File::create(path)
            .await
            .context("Failed to create CSV file")?;

        // Write the header
        file.write_all(b"time_offset,x1,y1,z1,x2,y2,z2\n")
            .await
            .context("Failed to write CSV header")?;
        let file = BufWriter::new(file);

        Ok(Self {
            file,
            buffer: String::new(),
        })
    }

    /// Log a sample to the CSV file
    async fn log(
        &mut self,
        time_offset: Duration,
        sample1: &Sample,
        sample2: &Sample,
    ) -> Result<()> {
        use std::fmt::Write;

        self.buffer.clear();
        write!(
            &mut self.buffer,
            "{},{},{},{},{},{},{}\n",
            time_offset.as_millis(),
            sample1.x,
            sample1.y,
            sample1.z,
            sample2.x,
            sample2.y,
            sample2.z,
        )
        .context("Failed to format sample")?;

        self.file
            .write_all(self.buffer.as_bytes())
            .await
            .context("Failed to write sample to CSV file")?;

        Ok(())
    }

    /// Flush the CSV writer
    async fn flush(&mut self) -> Result<()> {
        self.file
            .flush()
            .await
            .context("Failed to flush CSV file")?;
        Ok(())
    }
}

/// Process a buffer of samples and call the provided callback for each sample
fn parse_samples(buffer: &[u8]) -> Vec<Sample> {
    buffer
        .chunks(FIFO_SAMPLE_SIZE)
        .flat_map(|chunk| {
            if chunk.len() == FIFO_SAMPLE_SIZE {
                // Little-endian 16-bit signed int
                let x = chunk[1] as i16 | (chunk[0] as i16) << 8;
                let y = chunk[3] as i16 | (chunk[2] as i16) << 8;
                let z = chunk[5] as i16 | (chunk[4] as i16) << 8;

                Some(Sample { x, y, z })
            } else {
                None
            }
        })
        .collect()
}

fn main() {
    // Set up Ctrl+C handler
    let running = Arc::new(std::sync::atomic::AtomicBool::new(true));
    let r = running.clone();
    ctrlc::set_handler(move || {
        eprintln!("\nReceived Ctrl+C, shutting down...");
        r.store(false, std::sync::atomic::Ordering::SeqCst);
        std::thread::sleep(Duration::from_millis(1500));
        std::process::exit(0);
    })
    .context("Error setting Ctrl+C handler")
    .unwrap();

    let gui = gui::LabrecordApp::default();

    let gui2 = gui.clone();
    let running2 = running.clone();

    std::thread::Builder::new()
        .name("data-recorder".to_string())
        .spawn(move || {
            let rt = tokio::runtime::Runtime::new()
                .context("Failed to create Tokio runtime")
                .unwrap();
            rt.block_on(app_loop(gui2, running2));
        })
        .unwrap();

    gui::run_gui(gui);

    running.store(false, std::sync::atomic::Ordering::SeqCst);
}

async fn app_loop(gui: LabrecordApp, running: Arc<AtomicBool>) {
    while let Err(e) = app(gui.clone(), running.clone()).await {
        eprintln!("Error in main app: {:?}", e);
    }
}

async fn app(gui: LabrecordApp, running: Arc<AtomicBool>) -> Result<()> {
    let _args = Cli::parse();
    let config = fs::read_to_string("config.toml").context("Failed to read config file")?;
    let config: Config = toml::from_str(&config).context("Failed to parse config file")?;

    let mut stdout = stdout().lock();

    let scenario_start = Local::now();
    let scenario_start = scenario_start.format("%Y-%m-%d_%H-%M-%S").to_string();

    let mut last_batch_start = Instant::now() - Duration::from_secs(10);

    'outer: while running.load(std::sync::atomic::Ordering::SeqCst) {
        // Connect to the server
        let stream: Box<dyn AsyncRead + Unpin + Send> = {
            writeln!(stdout, "Opening serial port {}", config.port)?;
            Box::new(
                match tokio_serial::SerialStream::open(&tokio_serial::new(&config.port, 115_200))
                    .context("Failed to open serial port")
                {
                    Ok(stream) => stream,
                    Err(e) => {
                        writeln!(stdout, "Error opening serial port: {}", e)?;
                        tokio::time::sleep(Duration::from_secs(2)).await;
                        writeln!(stdout, "Retrying...")?;
                        continue 'outer;
                    }
                },
            )
        };
        let mut stream = tokio::io::BufReader::new(stream);
        writeln!(stdout, "Connected!")?;

        let mut buffer = vec![0u8; 1024];

        const S: &'static str = "START";

        // read a full frame
        stream
            .read_exact(&mut buffer[..8 * FIFO_SAMPLE_SIZE + S.len()])
            .await?;
        let Some(start) = buffer[..8 * FIFO_SAMPLE_SIZE + S.len()]
            .windows(5)
            .position(|w| w == S.as_bytes())
        else {
            writeln!(stdout, "No start word found")?;
            continue;
        };
        // skip enough bytes to be on packet boundary
        // x <start> START <start+5> y
        let skip = start + S.len();
        stream
            .read_exact(&mut buffer[..skip])
            .await
            .context("Failed to read from stream")?;

        while running.load(std::sync::atomic::Ordering::SeqCst) {
            // Create the CSV logger
            // ensure that we do not reuse the same filename
            tokio::time::sleep_until((last_batch_start + Duration::from_secs(1)).into()).await;
            let batch_start = Local::now();
            let batch_start = batch_start.format("%Y-%m-%d_%H-%M-%S").to_string();
            last_batch_start = Instant::now();

            let output_path = PathBuf::from(format!("readings/readings-{}.csv", batch_start));
            println!("Logging to {}", output_path.display());
            tokio::fs::write(
                output_path.with_extension("json"),
                serde_json::json!({
                    "scenario": config.scenario,
                    "batch_start": batch_start,
                    "scenario_start": scenario_start,
                    "hostname": gethostname().to_string_lossy(),
                    "source": config.port,
                })
                .to_string(),
            )
            .await
            .context("Failed to write metadata JSON")?;
            let mut csv_logger = CsvLogger::new(&output_path).await?;

            if let Err(e) = do_batch(&mut stream, &mut csv_logger, &gui).await {
                writeln!(stdout, "Error while collecting batch: {}", e)?;
                let _ = csv_logger.flush().await;
                continue 'outer;
            }

            let _ = csv_logger.flush().await;
            writeln!(stdout, "Batch complete")?;
        }
    }

    writeln!(stdout, "Shutting down...")?;

    Ok(())
}

async fn add_samples(sensor: &Arc<Mutex<gui::SensorData>>, time_offset: Duration, sample: &Sample) {
    let mut sensor = sensor.lock().await;
    sensor.x.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.x as f64,
    });
    sensor.y.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.y as f64,
    });
    sensor.z.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.z as f64,
    });
}

async fn do_batch(
    stream: &mut (dyn AsyncRead + Unpin),
    csv_logger: &mut CsvLogger,
    gui: &LabrecordApp,
) -> Result<()> {
    let batch_start = Instant::now() - TIME_PER_SAMPLE * 4;

    let mut buffer = vec![0u8; 1024];

    for _ in 0..(SAMPLES_PER_SECOND * 60 * 60) / 4 {
        stream
            .read_exact(&mut buffer[..4 * FIFO_SAMPLE_SIZE])
            .await?;
        let samples1 = parse_samples(&buffer[..FIFO_SAMPLE_SIZE * 4]);
        stream
            .read_exact(&mut buffer[..4 * FIFO_SAMPLE_SIZE])
            .await?;
        let samples2 = parse_samples(&buffer[..FIFO_SAMPLE_SIZE * 4]);

        let now = Instant::now();
        for (i, (sample1, sample2)) in samples1.iter().zip(samples2.iter()).enumerate() {
            let time = now - (4 - i) as u32 * TIME_PER_SAMPLE;
            let time_offset = time - batch_start;
            csv_logger
                .log(time_offset, sample1, sample2)
                .await
                .context("Failed to log sample")?;
            add_samples(&gui.sensor1, time_offset, sample1).await;
            add_samples(&gui.sensor2, time_offset, sample2).await;
        }

        // skip START word
        stream
            .read_exact(&mut buffer[..5])
            .await
            .context("Failed to read from stream")?;

        if &buffer[..5] != b"START" {
            anyhow::bail!("Expected START word, got {:?}", &buffer[..5]);
        }
    }

    Ok(())
}
