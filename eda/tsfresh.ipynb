import pandas as pd
from tsfresh import extract_features
from tsfresh.feature_extraction import MinimalFCParameters

from eda import kk05

df = kk05.sensor1()
df = df[(df.index >= "2025-05-19 16:00:00") & (df.index <= "2025-05-19 16:10:00")]
df = df[["x", "y", "z"]]

df=(df-df.mean())/df.std()

df.head()

df.plot()

import numpy as np

def window_df(df: pd.DataFrame, window_size: int, overlap: int) -> pd.DataFrame:
    """
    Slice df into overlapping windows of length `window_size`,
    with each window shifting by (window_size - overlap) rows.
    Returns a long DataFrame with an added 'window_id'.
    """
    step = window_size - overlap
    # all valid window start positions
    starts = np.arange(0, len(df) - window_size + 1, step)
    
    # build each window and tag it
    windows = [
        df.iloc[start : start + window_size]
          .assign(id=window_idx, time=df.index[start])
        for window_idx, start in enumerate(starts)
    ]
    
    # concatenate into one DataFrame
    return pd.concat(windows, ignore_index=True)

from tsfresh.feature_extraction import ComprehensiveFCParameters
import re

df["id"] = 1
W = 200
df_rolled = window_df(df, W, W//2)

spec_params = {
    "fft_coefficient":       [{"coeff": i, "attr": "abs"} for i in range(1, 25)],
    # "energy_ratio_by_chunks": [{"num_segments": 4, "segment_focus": j} for j in range(4)]
}

# 2) Extract your minimal-set features on each window:
features = extract_features(
    df_rolled,
    column_id="id",
    column_sort="time",
    default_fc_parameters=spec_params
)

features.rename(columns=lambda x: re.sub("x__fft_coefficient__attr_\"abs\"__coeff_(\\d+)", "x_fft_\\1_hz", x), inplace=True)
features.rename(columns=lambda x: re.sub("y__fft_coefficient__attr_\"abs\"__coeff_(\\d+)", "y_fft_\\1_hz", x), inplace=True)
features.rename(columns=lambda x: re.sub("z__fft_coefficient__attr_\"abs\"__coeff_(\\d+)", "z_fft_\\1_hz", x), inplace=True)

features.head()

import matplotlib.pyplot as plt

cols = features.columns
# only cols starting with x1
cols = [col for col in cols if col.startswith("x_fft")]

spectral = features[cols].std(axis=0)
spectral.head()


spectral.plot.bar(figsize=(12, 6))
plt.show()


fig, axes = plt.subplots(len(cols), 1, figsize=(8, 2*len(cols)), sharex=True)

for ax, feat in zip(axes, cols):
    ax.set_title(feat)
    ax.plot(features.index, features[feat])

axes[-1].set_xlabel('Time')
plt.show()