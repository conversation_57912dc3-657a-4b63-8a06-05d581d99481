#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/i2c.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_rom_sys.h" // for esp_rom_delay_us()
#include <string.h>      // for memcpy

// I2C Bus 0 Configuration (Sensor 1)
#define I2C_BUS_0 I2C_NUM_0
#define I2C_BUS_0_SDA_IO 15
#define I2C_BUS_0_SCL_IO 17

// I2C Bus 1 Configuration (Sensor 2)
#define I2C_BUS_1 I2C_NUM_1
#define I2C_BUS_1_SDA_IO 12
#define I2C_BUS_1_SCL_IO 14

// Common I2C Configuration
#define I2C_MASTER_FREQ_HZ 400000
#define I2C_MASTER_TIMEOUT_MS 1000

// UART Configuration
#define UART_PORT_NUM UART_NUM_0
#define UART_BAUD_RATE 921600 // High baud rate for maximum throughput
#define UART_TX_PIN 43
#define UART_RX_PIN 44
#define UART_BUF_SIZE 1024

// ADXL345 Configuration
#define ADXL345_ADDR 0x53 // Standard address
#define MULTI_BYTE_BIT 0x80

// ADXL345 registers
enum
{
    REG_DEVID = 0x00,
    REG_BW_RATE = 0x2C,
    REG_POWER_CTL = 0x2D,
    REG_DATA_FORMAT = 0x31,
    REG_FIFO_CTL = 0x38,
    REG_FIFO_STATUS = 0x39,
    REG_DATAX0 = 0x32
};

// 800 Hz ODR, full-res ±16 g
#define ADXL345_ODR_CODE 0x0D
#define BURST_SAMPLES 16 // watermark & block size

// Task configuration
#define SENSOR_TASK_STACK_SIZE 4096
#define SENSOR_TASK_PRIORITY 5
#define DATA_QUEUE_SIZE 64

static const char *TAG = "DUAL_ADXL345";

// Sensor data structure
typedef struct
{
    uint8_t sensor_id;
    uint32_t timestamp;
    int16_t accel[BURST_SAMPLES][3];
    uint8_t sample_count;
} sensor_data_t;

// Binary UART packet structure for efficient transmission
typedef struct __attribute__((packed))
{
    uint8_t header[4];               // "ADXL" magic header
    uint8_t sensor_id;               // Sensor ID (1 or 2)
    uint32_t timestamp;              // Timestamp
    uint8_t sample_count;            // Number of samples (should be 16)
    int16_t accel[BURST_SAMPLES][3]; // Raw accelerometer data
    uint16_t checksum;               // Simple checksum for data integrity
} uart_packet_t;

// Simple sensor configuration structure
typedef struct
{
    uint8_t sensor_id;
    i2c_port_t i2c_port;
    const char *name;
} sensor_config_t;

// Global data queue for sensor data
static QueueHandle_t sensor_data_queue;

// Write single byte to register for a specific sensor
static esp_err_t write_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, val, true);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read len bytes starting at reg for a specific sensor. If len>1, OR-in MULTI_BYTE_BIT
static esp_err_t read_regs(i2c_port_t i2c_port, uint8_t reg, uint8_t *buf, size_t len)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    // send register address (with auto-increment bit if a multi-byte read)
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg | (len > 1 ? MULTI_BYTE_BIT : 0), true);
    // read back len bytes
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    if (len > 1)
    {
        i2c_master_read(cmd, buf, len - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, buf + len - 1, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);
    return err;
}

// Initialize a specific I2C bus
static esp_err_t i2c_bus_init(i2c_port_t i2c_port, int sda_io, int scl_io)
{
    i2c_config_t cfg = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = sda_io,
        .scl_io_num = scl_io,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };
    esp_err_t err = i2c_param_config(i2c_port, &cfg);
    if (err != ESP_OK)
        return err;
    return i2c_driver_install(i2c_port, cfg.mode, 0, 0, 0);
}

// I2C scanner function to detect devices on a bus
static void i2c_scan_bus(i2c_port_t i2c_port, const char *bus_name)
{
    ESP_LOGI(TAG, "Scanning %s for I2C devices...", bus_name);
    int devices_found = 0;

    for (uint8_t addr = 0x08; addr <= 0x77; addr++)
    {
        i2c_cmd_handle_t cmd = i2c_cmd_link_create();
        i2c_master_start(cmd);
        i2c_master_write_byte(cmd, (addr << 1) | I2C_MASTER_WRITE, true);
        i2c_master_stop(cmd);
        esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, pdMS_TO_TICKS(50));
        i2c_cmd_link_delete(cmd);

        if (err == ESP_OK)
        {
            ESP_LOGI(TAG, "  Device found at address 0x%02X", addr);
            devices_found++;
        }
    }

    if (devices_found == 0)
    {
        ESP_LOGW(TAG, "  No I2C devices found on %s", bus_name);
    }
    else
    {
        ESP_LOGI(TAG, "  Found %d device(s) on %s", devices_found, bus_name);
    }
}

// Initialize all I2C buses
static esp_err_t i2c_master_init(void)
{
    esp_err_t err;

    // Initialize I2C Bus 0
    err = i2c_bus_init(I2C_BUS_0, I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 0: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 0 initialized (SDA=%d, SCL=%d)", I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);

    // Initialize I2C Bus 1
    err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 1: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 1 initialized (SDA=%d, SCL=%d)", I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);

    // Scan both buses for devices
    vTaskDelay(pdMS_TO_TICKS(100)); // Give buses time to stabilize
    i2c_scan_bus(I2C_BUS_0, "I2C Bus 0");
    i2c_scan_bus(I2C_BUS_1, "I2C Bus 1");

    return ESP_OK;
}

// Initialize UART for high-speed data transmission
static esp_err_t uart_init(void)
{
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    esp_err_t err = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 0, NULL, 0);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_param_config(UART_PORT_NUM, &uart_config);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "UART initialized: %d baud, TX=%d, RX=%d", UART_BAUD_RATE, UART_TX_PIN, UART_RX_PIN);
    return ESP_OK;
}

// Initialize a specific ADXL345 sensor
static esp_err_t adxl345_init(const sensor_config_t *sensor)
{
    uint8_t id = 0;
    esp_err_t err = read_regs(sensor->i2c_port, REG_DEVID, &id, 1);

    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read DEVID from %s: %s", sensor->name, esp_err_to_name(err));
        return ESP_FAIL;
    }

    if (id != 0xE5)
    {
        ESP_LOGE(TAG, "Invalid DEVID on %s: expected 0xE5, got 0x%02X", sensor->name, id);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "ADXL345 detected on %s (DEVID=0x%02X)", sensor->name, id);

    // 1) Set output data rate to 800 Hz
    ESP_ERROR_CHECK(write_reg(sensor->i2c_port, REG_BW_RATE, ADXL345_ODR_CODE));
    // 2) ±16 g, full-resolution (FULL_RES=1, Range=11)
    ESP_ERROR_CHECK(write_reg(sensor->i2c_port, REG_DATA_FORMAT, 0x0B));
    // 3) Enable measurement mode
    ESP_ERROR_CHECK(write_reg(sensor->i2c_port, REG_POWER_CTL, 0x08));
    // 4) FIFO: stream mode (10), watermark = BURST_SAMPLES
    ESP_ERROR_CHECK(write_reg(sensor->i2c_port, REG_FIFO_CTL, (2 << 6) | BURST_SAMPLES));

    ESP_LOGI(TAG, "%s initialized: ±16g, FIFO stream @800Hz, burst=%d", sensor->name, BURST_SAMPLES);
    return ESP_OK;
}

// Calculate simple checksum for data integrity
static uint16_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint16_t checksum = 0;
    for (size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return checksum;
}

// Send sensor data via UART in binary format - extremely efficient
static void send_uart_data(const sensor_data_t *data)
{
    uart_packet_t packet;

    // Set magic header
    packet.header[0] = 'A';
    packet.header[1] = 'D';
    packet.header[2] = 'X';
    packet.header[3] = 'L';

    // Copy sensor data
    packet.sensor_id = data->sensor_id;
    packet.timestamp = data->timestamp;
    packet.sample_count = data->sample_count;

    // Copy accelerometer data directly (no conversion needed)
    memcpy(packet.accel, data->accel, sizeof(packet.accel));

    // Calculate checksum (exclude checksum field itself)
    packet.checksum = calculate_checksum((uint8_t *)&packet, sizeof(packet) - sizeof(packet.checksum));

    // Send entire packet in one UART write for maximum efficiency
    uart_write_bytes(UART_PORT_NUM, &packet, sizeof(packet));
}

// Sensor reading task - runs independently for each sensor
static void sensor_task(void *pvParameters)
{
    sensor_config_t *sensor = (sensor_config_t *)pvParameters;
    sensor_data_t sensor_data;
    uint8_t raw[6];

    ESP_LOGI(TAG, "Starting sensor task for %s", sensor->name);

    // Initialize the sensor
    if (adxl345_init(sensor) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize %s, terminating task", sensor->name);
        vTaskDelete(NULL);
        return;
    }

    // Initialize sensor data structure
    sensor_data.sensor_id = sensor->sensor_id;

    while (1)
    {
        // 1) Wait until FIFO has ≥ BURST_SAMPLES entries
        uint8_t status;
        do
        {
            if (read_regs(sensor->i2c_port, REG_FIFO_STATUS, &status, 1) != ESP_OK)
            {
                ESP_LOGW(TAG, "%s: Failed to read FIFO status", sensor->name);
                vTaskDelay(pdMS_TO_TICKS(10));
                continue;
            }
            vTaskDelay(pdMS_TO_TICKS(1));
        } while ((status & 0x3F) < BURST_SAMPLES);

        // 2) Check for overflow and reset if needed
        if (status & 0x80)
        {
            ESP_LOGW(TAG, "%s: FIFO overflow! resetting...", sensor->name);
            write_reg(sensor->i2c_port, REG_FIFO_CTL, 0x00);
            write_reg(sensor->i2c_port, REG_FIFO_CTL, (2 << 6) | BURST_SAMPLES);
            continue;
        }

        // 3) Record timestamp and read samples
        sensor_data.timestamp = xTaskGetTickCount();
        sensor_data.sample_count = BURST_SAMPLES;

        // Pop and read each sample in its own 6-byte burst
        for (int i = 0; i < BURST_SAMPLES; i++)
        {
            if (read_regs(sensor->i2c_port, REG_DATAX0, raw, 6) != ESP_OK)
            {
                ESP_LOGW(TAG, "%s: Failed to read sample %d", sensor->name, i);
                break;
            }

            sensor_data.accel[i][0] = (int16_t)((raw[1] << 8) | raw[0]);
            sensor_data.accel[i][1] = (int16_t)((raw[3] << 8) | raw[2]);
            sensor_data.accel[i][2] = (int16_t)((raw[5] << 8) | raw[4]);

            // datasheet requires ≥5 µs between FIFO reads; I2C bus time already covers this,
            // but to be safe:
            esp_rom_delay_us(5);
        }

        // 4) Send data to queue (non-blocking)
        if (xQueueSend(sensor_data_queue, &sensor_data, 0) != pdTRUE)
        {
            ESP_LOGW(TAG, "%s: Data queue full, dropping samples", sensor->name);
        }
    }
}

// Data processing task - handles data from both sensors
static void data_processing_task(void *pvParameters)
{
    size_t num_samples = 0;
    int last_log_time = 0;

    sensor_data_t received_data;

    ESP_LOGI(TAG, "Starting data processing task");

    while (1)
    {
        if (xTaskGetTickCount() - last_log_time > pdMS_TO_TICKS(1000))
        {
            ESP_LOGI(TAG, "Samples per second: %zu/s", num_samples);
            num_samples = 0;
            last_log_time = xTaskGetTickCount();
        }

        // Wait for data from any sensor
        if (xQueueReceive(sensor_data_queue, &received_data, portMAX_DELAY) == pdTRUE)
        {
            num_samples += received_data.sample_count;

            // Send all 16 samples via UART in one efficient binary packet
            send_uart_data(&received_data);
        }
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "Starting dual ADXL345 sensor firmware");

    // Initialize I2C buses
    ESP_ERROR_CHECK(i2c_master_init());

    // Initialize UART for data transmission
    ESP_ERROR_CHECK(uart_init());

    // Create data queue
    sensor_data_queue = xQueueCreate(DATA_QUEUE_SIZE, sizeof(sensor_data_t));
    if (sensor_data_queue == NULL)
    {
        ESP_LOGE(TAG, "Failed to create data queue");
        return;
    }

    // Define sensor configurations
    static sensor_config_t sensor1 = {
        .sensor_id = 1,
        .i2c_port = I2C_BUS_0,
        .name = "Sensor1_Bus0"};

    static sensor_config_t sensor2 = {
        .sensor_id = 2,
        .i2c_port = I2C_BUS_1,
        .name = "Sensor2_Bus1"};

    // Create data processing task
    xTaskCreate(data_processing_task, "data_proc", SENSOR_TASK_STACK_SIZE, NULL,
                SENSOR_TASK_PRIORITY, NULL);

    // Create sensor tasks
    xTaskCreate(sensor_task, "sensor1", SENSOR_TASK_STACK_SIZE, &sensor1,
                SENSOR_TASK_PRIORITY + 1, NULL);
    xTaskCreate(sensor_task, "sensor2", SENSOR_TASK_STACK_SIZE, &sensor2,
                SENSOR_TASK_PRIORITY + 1, NULL);

    ESP_LOGI(TAG, "All tasks created successfully");
    ESP_LOGI(TAG, "Sensor 1: I2C Bus %d (SDA=%d, SCL=%d), Address=0x%02X",
             I2C_BUS_0, I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO, ADXL345_ADDR);
    ESP_LOGI(TAG, "Sensor 2: I2C Bus %d (SDA=%d, SCL=%d), Address=0x%02X",
             I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO, ADXL345_ADDR);

    // Main task can now idle - all work is done by the created tasks
    while (1)
    {
        vTaskDelay(pdMS_TO_TICKS(10000)); // Sleep for 10 seconds
        ESP_LOGI(TAG, "Main task heartbeat - system running");
    }
}
