"�rA�-14  -25  267
[1]  -14  -26  262
[1]   -7  -18  258
[1]  -14  -23  261
[1]  -17  -20  271
[2]  132  214  -17
[2]  140  212   -3
[2]  136  221  -15
[2]  130  216  -16
[2]  129  �ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bce4h ( 48356) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bd0c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e7a8 vaddr=40374000 size=01870h (  6256) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a53ch (107836) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a564 vaddr=40375870 size=0df70h ( 57200) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (197) app_init: App version:      a6eb462-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:47:07[0m
[0;32mI (208) app_init: ELF file SHA256:  99953b102...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (273) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (441) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (441) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (451) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (451) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (461) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (461) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (461) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (471) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (471) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (481) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (491) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (491) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[1]  -13  -24  262
[0;32mI (501) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[1]  -13  -30  262
[1]  -16  -25  265
[1]  -14  -25  265
[1]  -14  -23  263
[1]  -10  -28  265
[1]  -13  -26  261
[1]  -15  -25  266
[1]  -11  -24  259
[1]  -13  -26  260
[1]  -13  -24  262
[1]  -13  -25  262
[1]  -18  -25  265
[1]  -13  -25  267
[1]  -15  -24  261
[1]  -14  -27  264
[1]  -16  -24  265
[1]  -13  -26  261
[1]  -15  -24  262
[1]  -10  -27  265
[1]  -13  -25  265
[1]  -10  -26  229
[1]  -11  -25  265
[1]  -11  -24  264
[1]  -11  -23  262
[1]  -14  -27  261
[1]  -13  -26  259
[1]  -13  -25  261
[1]  -13  -22  262
[1]  -13  -27  263
[1]  -13  -26  263
[1]  -12  -27  264
[1]  -19  -24  263
[1]  -11  -30  257
[1]  -17  -30  267
[0;32mI (491) DUAL_ADXL345: All tasks created successfully[0m
[1]  -13  -28  264
[1]  -14  -23  257
[1]  -18  -23  262
[1]  -13  -21  264
[1]  -14  -19  270
[1]  -13  -30  258
[1]  -10  -25  269
[1]  -21  -21  263
[1]  -16  -28  264
[1]  -10  -24  265
[1]  -22  -23  258
[1]  -10  -29  264
[1]  -15  -33  262
[2]  112  190  -16
[2]  126  218  -12
[2]  126  217  -12
[2]  130  213  -15
[2]  128  216  -13
[2]  127  215  -13
[2]  127  216  -14
[2]  131  217  -17
[2]  128  206  -25
[2]  124  214  -10
[2]  126  222   -5
[2]  124  209  -12
[2]  132  214  -19
[2]  125  217   -5
[2]  129  207  -21
[2]  118  207  -17
[1]  -15  -26  262
[1]  -16  -25  263
[1]  -10  -14  263
[1]  -15  -21  267
[1]  -15  -17  263
[1]  -10  -25  263
[1]  -15  -29  261
[1]  -17  -27  263
[1]  -10  -29  260
[1]   -9  -28  262
[1]  -12  -27  263
[1]  -13  -21  266
[1]  -16  -35  267
[1]   -6  -24  267
[1]  -15  -21  269
[1]  -21  -25  261
[2]  132  220   -8
[2]  128  216   -8
[2]  128  225  -11
[2]  132  223   -5
[2]  135  219  -20
[2]  124  214  -22
[2]  129  223   -9
[2]  122  202  -25
[2]  126  227   -7
[2]  128  212  -22
[2]  119  206  -24
[2]  130  217  -23
[2]  135  224   -3
[2]  126  210  -18
[2]  131  211  -10
[2]  130  223  -10
[0;32mI (581) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[1]   -6  -25  264
[1]  -17  -23  258
[1]  -13  -24  256
[1]   -8  -22  263
[1]  -11  -24  259
[1]  -13  -26  264
[1]  -13  -24  261
[1]  -16  -24  263
[1]  -18  -24  261
[1]  -16  -20  257
[1]  -13  -26  261
[1]  -12  -31  250
[1]  -15  -28  263
[1]  -13  -30  269
[1]   -8  -26  263
[1]  -16  -25  269
[2]  123  212  -21
[2]  136  226   -9
[2]  135  216  -16
[2]  124  209  -25
[2]  123  208  -29
[2]  124  202  -21
[2]  130  216  -12
[2]  130  211  -23
[2]  122  220  -17
[2]  135  226  -17
[2]  124  204  -24
[2]  118  207  -27
[2]  126  219   -6
[2]  137  224   -9
[2]  122  211  -18
[2]  128  217  -21
[1]  -12  -29  262
[1]   -9  -31  259
[1]   -8  -15  258
[1]   -7  -26  262
[1]  -14  -26  267
[1]  -15  -34  259
[1]  -13  -27  258
[1]   -7  -29  260
[1]   -3  -33  264
[1]  -16  -26  261
[1]  -17  -20  257
[1]  -14  -20  258
[1]  -21  -20  267
[1]  -16  -23  260
[1]   -9  -30  258
[1]  -12  -20  263
[2]  125  209  -21
[2]  135  225  -17
[2]  130  211  -18
[2]  120  210  -21
[2]  121  211  -19
[2]  122  213  -11
[2]  117  204  -27
[2]  128  217  -14
[2]  136  221    0
[2]  134  218  -24
[2]  121  209  -16
[2]  133  225   -4
[2]  127  213  -24
[2]  133  212  -17
[2]  121  211  -14
[2]  132  226   -8
[1]  -12  -27  271
[1]   -6  -30  257
[1]  -18  -25  262
[1]  -22  -31  258
[1]  -12  -35  262
[1]  -17  -27  271
[1]  -15  -25  258
[1]  -16  -35  257
[1]  -10  -27  260
[1]  -12  -22  259
[1]   -5  -26  263
[1]  -14  -18  262
[1]  -15  -27  258
[1]  -10  -26  267
[1]  -14  -24  260
[1]   -9  -34  268
[2]  127  208  -21
[2]  133  225   -9
[2]  140  213  -18
[2]  128  216  -20
[2]  121  203  -23
[2]  126  219  -17
[2]  125  205  -25
[2]  118  217  -18
[2]  131  211  -20
[2]  119  207  -19
[2]  133  230  -10
[2]  129  205  -27
[2]  130  220   -7
[2]  130  224  -10
[2]  133  216  -19
[2]  136  213  -20
[1]  -14  -26  266
[1]   -4  -27  267
[1]   -5  -28  267
[1]   -7  -18  261
[1]   -9  -22  265
[1]  -17  -28  262
[1]  -12  -29  259
[1]  -13  -25  263
[1]   -8  -25  260
[1]  -17  -29  260
[1]   -8  -26  264
[1]  -16  -27  272
[1]  -22  -20  261
[1]  -11  -24  260
[1]  -23  -24  254
[1]  -17  -27  263
[2]  131  211  -22
[2]  138  226   -6
[2]  129  222  -19
[2]  120  217  -21
[2]  124  209  -23
[2]  124  205  -24
[2]  127  209  -28
[2]  119  211  -11
[2]  138  213  -23
[2]  121  207  -15
[2]  130  226  -16
[2]  127  223   -4
[2]  138  222  -19
[2]  119  208  -19
[2]  122  208  -17
[2]  127  220   -2
[1]  -10  -18  265
[1]  -13  -27  262
[1]  -14  -28  258
[1]   -4  -27  271
[1]  -21  -15  257
[1]  -17  -19  260
[1]  -18  -25  261
[1]  -15  -23  260
[1]  -19  -21  272
[1]  -13  -25  259
[1]  -12  -22  260
[1]   -8  -28  258
[1]   -9  -20  263
[1]  -22  -22  265
[1]  -17  -19  265
[1]   -8  -25  263
[2]  124  214  -22
[2]  141  228   -9
[2]  131  220  -13
[2]  126  210  -31
[2]  123  212  -24
[2]  121  206  -18
[2]  120  206  -26
[2]  121  209  -13
[2]  126  215  -21
[2]  120  206  -12
[2]  127  220    3
[2]  120  212  -19
[2]  121  216  -10
[2]  128  210  -21
[2]  127  213   -4
[2]  142  218  -16
[1]  -15  -30  259
[1]  -16  -27  262
[1]  -16  -18  265
[1]  -11  -31  262
[1]   -7  -19  271
[1]  -19  -24  268
[1]  -12  -34  257
[1]  -15  -23  270
[1]  -16  -23  266
[1]  -14  -21  269
[1]  -12  -26  267
[1]   -7  -25  261
[1]  -10  -31  259
[1]  -11  -26  268
[1]  -16  -25  268
[1]  -13  -25  263
[2]  120  208  -18
[2]  128  219  -21
[2]  125  209  -28
[2]  121  209  -21
[2]  124  212  -20
[2]  120  217  -15
[2]  128  205  -28
[2]  116  203  -17
[2]  131  227  -11
[2]  122  215  -25
[2]  128  215   -8
[2]  131  217  -13
[2]  121  210  -16
[2]  136  222   -9
[2]  127  212  -25
[2]  123  209  -12
[1]  -15  -26  261
[1]  -19  -24  258
[1]   -4  -17  260
[1]  -14  -20  266
[1]  -15  -23  260
[1]   -1  -19  259
[1]  -12  -25  268
[1]  -18  -20  264
[1]  -12  -25  263
[1]  -13  -24  263
[1]  -17  -22  265
[1]  -18  -30  258
[1]  -12  -26  259
[1]  -13  -30  255
[1]  -12  -27  256
[1]  -13  -22  264
[2]  136  216  -18
[2]  138  227   -7
[2]  133  217  -13
[2]  133  219  -17
[2]  137  212  -27
[2]  124  207  -26
[2]  126  204  -26
[2]  121  201  -26
[2]  120  213   -7
[2]  132  230  -13
[2]  120  206  -22
[2]  129  216  -27
[2]  120  208  -26
[2]  128  217  -19
[2]  122  214  -13
[2]  122  210  -17
[1]  -14  -26  261
[1]  -13  -33  258
[1]  -11  -29  266
[1]   -9  -18  267
[1]  -14  -21  263
[1]  -10  -27  264
[1]  -12  -29  262
[1]  -13  -27  259
[1]  -20  -16  259
[1]  -16  -21  260
[1]  -19  -23  278
[1]  -17  -22  264
[1]  -14  -25  266
[1]  -17  -31  265
[1]  -12  -24  260
[1]  -13  -27  263
[2]  128  229   -8
[2]  129  220  -11
[2]  122  210  -27
[2]  119  205  -28
[2]  122  212  -17
[2]  135  215   -5
[2]  121  210  -18
[2]  140  218  -18
[2]  122  208  -26
[2]  124  216   -9
[2]  131  217  -20
[2]  137  216   -8
[2]  132  215  -21
[2]  125  214  -13
[2]  136  223  -15
[2]  124  209  -18
[1]   -5  -22  264
[1]  -10  -21  259
[1]  -12  -30  256
[1]   -2  -25  266
[1]   -6  -25  262
[1]  -15  -24  263
[1]  -20  -20  263
[1]   -8  -28  270
[1]  -12  -25  262
[1]  -15  -26  262
[1]   -9  -27  261
[1]   -5  -25  261
[1]  -10  -22  266
[1]  -20  -26  258
[1]  -11  -21  260
[1]  -13  -32  260
[2]  137  223  -15
[2]  136  226  -10
[2]  135  223  -11
[2]  120  217  -23
[2]  123  215  -25
[2]  123  206  -29
[2]  137  209  -28
[2]  121  208  -21
[2]  135  222  -18
[2]  120  207  -23
[2]  131  225   -3
[2]  123  204  -28
[2]  126  207  -24
[2]  135  218  -24
[2]  124  211  -25
[2]  125  221  -22
[1]   -5  -26  263
[1]  -12  -23  270
[1]   -9  -18  262
[1]  -16  -20  259
[1]  -12  -30  262
[1]  -13  -29  256
[1]  -20  -22  261
[1]  -10  -27  266
[1]  -18  -20  259
[1]   -9  -32  266
[1]  -17  -22  265
[1]  -15  -19  257
[1]  -17  -28  258
[1]  -19  -22  257
[1]  -12  -26  262
[1]  -17  -30  266
[2]  117  213  -10
[2]  122  215  -18
[2]  127  211  -18
[2]  115  208  -18
[2]  116  213  -20
[2]  124  213  -10
[2]  124  205  -31
[2]  119  211  -12
[2]  134  216  -17
[2]  121  209  -23
[2]  130  222   -4
[2]  122  211  -27
[2]  115  210  -19
[2]  126  224   -5
[2]  118  212  -12
[2]  118  215   -7
[1]  -11  -24  270
[1]  -16  -23  260
[1]   -5  -25  262
[1]   -5  -32  263
[1]   -9  -33  260
[1]  -14  -22  265
[1]   -9  -27  258
[1]   -3  -20  262
[1]  -12  -24  256
[1]  -13  -27  263
[1]  -11  -22  259
[1]  -10  -27  261
[1]  -17  -25  260
[1]  -16  -23  262
[1]  -12  -24  276
[1]  -19  -26  263
[2]  136  224   -2
[2]  137  224   -8
[2]  142  217  -14
[2]  132  216  -19
[2]  126  215  -20
[2]  122  209  -24
[2]  129  217  -18
[2]  122  209  -28
[2]  128  225   -3
[2]  125  209  -28
[2]  120  217    0
[2]  140  229  -14
[2]  127  215  -22
[2]  124  218  -13
[2]  125  210  -17
[2]  128  231   -6
[1]  -12  -27  262
[1]  -11  -24  264
[1]   -6  -23  266
[1]  -16  -23  263
[1]  -11  -24  264
[1]  -11  -23  255
[1]  -13  -25  264
[1]   -4  -32  273
[1]  -13  -23  270
[1]  -12  -30  269
[1]  -22  -25  259
[1]  -14  -24  266
[1]  -15  -19  268
[1]   -6  -19  270
[1]   -9  -26  265
[1]  -13  -20  266
[0;32mI (1331) DUAL_ADXL345: Samples per second: 464/s[0m
[2]  124  214  -23
[2]  134  222  -14
[2]  129  215  -22
[2]  126  207  -25
[2]  119  204  -23
[2]  120  209  -15
[2]  121  205  -28
[2]  129  226   -7
[2]  123  206  -23
[2]  126  213   -3
[2]  135  217  -18
[2]  117  209  -11
[2]  123  211  -18
[2]  122  217  -11
[2]  123  215   -9
[2]  132  230   -7
[1]  -11  -28  264
[1]   -8  -28  268
[1]   -9  -22  262
[1]   -7  -26  261
[1]   -9  -29  258
[1]  -16  -26  259
[1]  -14  -20  267
[1]  -13  -25  269
[1]  -13  -25  265
[1]  -15  -27  262
[1]  -11  -23  258
[1]  -13  -21  263
[1]   -6  -26  266
[1]  -10  -22  256
[1]  -20  -23  258
[1]  -14  -27  264
[2]  141  221  -14
[2]  127  220  -16
[2]  133  214   -8
[2]  131  228  -12
[2]  129  222  -15
[2]  127  216   -9
[2]  126  209   -5
[2]  131  213  -23
[2]  115  207  -16
[2]  131  226  -15
[2]  121  205  -20
[2]  134  212   -6
[2]  120  219  -12
[2]  139  217  -21
[2]  122  215  -20
[2]  125  212  -22
[1]   -5  -23  273
[1]  -17  -14  259
[1]  -17  -33  263
[1]  -15  -23  266
[1]  -12  -26  260
[1]  -11  -27  255
[1]   -8  -24  266
[1]  -14  -22  264
[1]  -14  -22  258
[1]  -17  -24  268
[1]  -15  -25  265
[1]  -11  -25  260
[1]   -7  -22  259
[1]  -12  -24  262
[1]  -11  -29  271
[1]  -11  -31  261
[2]  130  215  -20
[2]  126  217   -8
[2]  130  228  -15
[2]  134  219   -2
[2]  129  220  -14
[2]  133  212  -27
[2]  123  224   -8
[2]  130  205  -25
[2]  126  213   -4
[2]  138  213  -17
[2]  121  210  -11
[2]  128  223  -16
[2]  131  221   -9
[2]  123  219   -8
[2]  126  215  -17
[2]  133  222   -2
[1]   -9  -25  258
[1]   -9  -23  269
[1]   -8  -20  262
[1]   -7  -24  260
[1]   -8  -29  268
[1]   -9  -24  263
[1]  -16  -27  255
[1]  -14  -24  262
[1]  -12  -24  262
[1]  -10  -27  260
[1]  -13  -24  261
[1]  -12  -26  260
[1]   -6  -24  265
[1]  -20  -19  266
[1]  -13  -24  262
[0;33mW (1501) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -19  -25  253
[2]  129  227  -16
[2]  131  225    3
[2]  136  222  -13
[2]  135  219  -18
[2]  129  213  -19
[2]  127  210  -20
[2]  122  212  -12
[0;33mW (1521) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  133  221   -7
[2]  120  205  -21
[2]  128  221    4
[2]  126  204  -24
[0;33mW (1531) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1541) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  119  206  -13
[2]  124  207  -24
[2]  137  223  -10
[2]  119  210  -24
[0;33mW (1551) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1561) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  131  218  -11
[1]  -13  -23  262
[1]    1  -32  267
[1]  -10  -26  271
[1]   -6  -27  264
[0;33mW (1581) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]   -8  -25  260
[1]  -13  -30  260
[1]   -9  -25  263
[1]  -19  -23  269
[1]  -13  -21  265
[0;33mW (1591) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1601) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -16  -31  255
[1]  -16  -31  266
[1]  -16  -24  263
[0;32mI (701) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;33mW (1611) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1621) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (1641) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1641) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -15  -25  261
[1]  -14  -26  266
[1]  -16  -24  261
[0;33mW (1661) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1661) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -18  -11  271
[2]  125  208  -19
[2]  142  229   -5
[2]  128  227  -17
[2]  130  216  -26
[2]  123  211  -20
[0;33mW (1681) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  123  215  -15
[2]  130  220   -9
[2]  121  202  -25
[2]  126  221   -9
[2]  125  208  -24
[0;33mW (1701) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1701) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  127  213   -7
[2]  127  217  -14
[2]  117  209  -12
[2]  131  225   -9
[0;33mW (1721) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1721) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  122  209  -15
[2]  122  211  -18
[1]  -16  -22  265
[1]  -12  -16  260
[1]  -19  -23  271
[0;33mW (1741) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -15  -29  264
[1]  -14  -22  256
[1]  -11  -22  257
[1]  -11  -21  256
[1]  -13  -23  267
[0;33mW (1761) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1761) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -14  -32  256
[1]  -11  -22  259
[1]  -12  -25  266
[1]  -14  -28  263
[0;33mW (1781) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1781) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -10  -28  254
[1]   -9  -23  264
[1]  -16  -25  263
[0;33mW (1801) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1801) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -16  -27  262
[2]  119  204  -20
[2]  127  212  -19
[2]  130  207  -23
[2]  127  211  -28
[2]  120  205  -19
[0;33mW (1821) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  127  214   -9
[2]  118  198  -31
[2]  124  222  -10
[2]  132  214  -21
[2]  122  210  -23
[0;33mW (1841) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1841) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  134  224  -12
[2]  127  209  -28
[2]  128  219  -12
[0;33mW (1861) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1861) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  126  210  -25
[2]  120  207  -23
[2]  132  220  -11
[1]  -14  -21  262
[1]  -17  -20  259
[1]  -11  -23  260
[0;33mW (1881) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]   -7  -22  262
[1]  -11  -25  257
[1]   -9  -25  263
[1]  -14  -18  264
[1]  -14  -26  264
[0;33mW (1901) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1901) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -14  -27  261
[1]   -9  -30  256
[1]  -20  -28  259
[0;33mW (1921) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1921) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -13  -23  266
[1]  -13  -24  263
[1]  -15  -24  258
[1]  -10  -26  260
[0;33mW (1941) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1941) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]   -9  -28  265
[2]  120  207  -21
[2]  136  220  -11
[2]  124  206  -19
[2]  125  205  -28
[0;33mW (1961) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  123  201  -20
[2]  123  207  -18
[2]  115  202  -24
[2]  122  216  -10
[2]  125  218  -22
[2]  118  208  -19
[0;33mW (1981) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (1981) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  129  230   -8
[2]  124  212  -12
[2]  125  209  -18
[0;33mW (2001) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2001) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  126  210  -13
[2]  119  209  -18
[2]  121  219  -10
[1]  -14  -26  258
[1]  -15  -31  254
[0;33mW (2021) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -23  -18  268
[1]  -18  -22  262
[1]  -17  -23  260
[1]   -7  -21  259
[1]  -14  -31  257
[1]  -12  -26  264
[0;33mW (2041) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2041) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -17  -34  265
[1]   -4  -30  258
[1]  -15  -26  254
[0;33mW (2061) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2061) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]    0  -29  262
[1]  -11  -27  260
[1]  -16  -28  254
[1]  -10  -25  255
[0;33mW (2081) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2081) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -13  -26  260
[2]  136  220   -2
[2]  130  217  -17
[2]  128  216    0
[0;33mW (2101) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  128  221  -11
[2]  131  225  -18
[2]  122  216  -23
[2]  128  217  -12
[2]  126  205  -22
[2]  129  222   -6
[2]  119  209  -19
[0;33mW (2121) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2121) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  124  216  -16
[2]  128  213  -16
[2]  128  214  -13
[0;33mW (2141) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2141) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  126  215   -7
[2]  139  222  -20
[2]  125  229  -11
[0;33mW (2161) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -16  -27  256
[1]  -25  -13  267
[1]   -7  -26  251
[1]  -14  -23  264
[1]  -10  -27  259
[1]  -11  -21  261
[1]  -13  -30  261
[1]   -9  -23  262
[0;33mW (2181) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2181) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -14  -27  265
[1]  -14  -31  259
[1]  -10  -30  257
[0;33mW (2201) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2201) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -14  -21  262
[1]  -14  -25  266
[1]  -12  -27  262
[1]   -9  -25  264
[0;33mW (2221) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2221) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -19  -24  262
[2]  127  223   -1
[2]  131  218  -22
[2]  131  212  -21
[0;33mW (2241) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  124  210  -24
[2]  120  207  -21
[2]  127  217  -21
[2]  117  202  -25
[2]  124  207  -25
[2]  126  220   -6
[0;33mW (2261) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2261) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  126  215  -27
[2]  121  218  -17
[2]  131  213  -18
[2]  118  213  -25
[0;33mW (2281) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2281) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  124  209  -24
[2]  119  211  -12
[2]  121  213  -21
[0;33mW (2301) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -15  -24  261
[1]  -13  -21  258
[1]  -18  -27  263
[1]   -5  -23  252
[1]   -7  -22  261
[1]  -11  -22  263
[1]  -13  -19  264
[1]  -14  -24  262
[0;33mW (2321) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2321) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -15  -29  260
[1]  -12  -26  263
[1]  -17  -26  258
[0;33mW (2341) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2351) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -20  -25  264
[1]   -8  -20  267
[1]  -16  -29  260
[0;33mW (2361) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2371) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -12  -22  257
[1]  -11  -22  264
[0;32mI (2381) DUAL_ADXL345: Samples per second: 288/s[0m
[0;33mW (2381) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2391) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  125  210  -20
[2]  131  223   -8
[2]  129  216  -19
[2]  123  209  -21
[0;33mW (2411) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  128  208  -21
[2]  119  211  -19
[2]  118  208  -30
[2]  135  219   -9
[2]  128  215  -20
[0;33mW (2421) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2431) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  129  217  -15
[2]  130  218  -18
[2]  119  213  -13
[2]  126  207  -18
[0;33mW (2441) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2451) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  124  223  -12
[2]  137  223  -18
[2]  127  217   -8
[0;33mW (2461) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -10  -26  269
[1]  -18  -24  260
[1]   -6  -18  263
[1]  -11  -27  260
[1]  -12  -18  268
[1]   -4  -25  263
[1]  -14  -21  265
[1]  -18  -22  262
[0;33mW (2481) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2491) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -14  -24  261
[1]   -5  -25  265
[1]  -16  -20  261
[0;33mW (2511) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2511) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -16  -16  261
[1]  -13  -30  257
[1]  -14  -29  259
[0;33mW (2531) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2531) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -11  -27  260
[1]  -14  -21  260
[2]  132  209  -20
[2]  139  221   -7
[0;33mW (2551) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  123  217  -19
[2]  118  207  -26
[2]  118  209  -22
[2]  122  215  -15
[2]  116  205  -25
[2]  131  217  -12
[2]  124  208  -26
[0;33mW (2571) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2571) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  127  218   -2
[2]  130  217  -22
[2]  121  215  -14
[2]  123  205  -20
[0;33mW (2591) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2591) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  123  211  -15
[2]  128  210  -26
[2]  126  225   -7
[0;33mW (2611) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -12  -25  258
[1]  -11  -20  266
[1]  -10  -21  258
[1]   -9  -26  262
[1]  -11  -25  264
[1]  -17  -24  264
[1]  -12  -25  266
[1]  -16  -20  260
[0;33mW (2631) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2631) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -13  -22  257
[1]  -13  -27  264
[1]  -18  -27  259
[0;33mW (2651) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2651) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -14  -20  262
[1]  -15  -25  275
[1]  -13  -30  261
[0;33mW (2671) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2671) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -15  -23  267
[1]   -9  -29  257
[2]  136  223   -8
[2]  130  229   -9
[0;33mW (2691) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  139  216   -1
[2]  134  215  -19
[2]  129  208  -16
[2]  124  209  -17
[2]  125  204  -21
[2]  123  215  -12
[2]  134  215  -21
[0;33mW (2711) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2711) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  123  206  -12
[2]  129  222  -12
[2]  122  206  -25
[0;33mW (2731) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2731) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  123  219  -10
[2]  126  218  -14
[2]  116  207  -22
[0;33mW (2751) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2751) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  124  222  -14
[1]  -13  -22  267
[1]   -5  -25  259
[1]  -14  -20  264
[0;33mW (2771) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -10  -26  261
[1]   -7  -18  256
[1]   -9  -19  267
[1]   -5  -28  257
[1]  -14  -28  266
[1]  -10  -26  267
[1]  -11  -19  271
[0;33mW (2791) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2791) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -14  -22  268
[1]  -13  -21  266
[1]  -15  -23  254
[0;33mW (2811) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2811) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]   -5  -25  261
[1]  -12  -21  256
[1]  -17  -25  259
[2]  121  211  -14
[0;33mW (2831) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  128  230  -13
[2]  130  222  -25
[2]  125  217  -15
[2]  131  209   -9
[2]  123  210  -22
[2]  129  223  -14
[2]  119  206  -23
[0;33mW (2851) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2851) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  135  218   -6
[2]  130  207  -26
[2]  130  218  -18
[2]  122  213  -22
[0;33mW (2871) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2871) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  122  208  -17
[2]  139  221  -17
[2]  135  216  -19
[0;33mW (2891) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2891) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  135  229    0
[1]  -16  -20  259
[1]   -4  -27  263
[1]  -14  -27  262
[0;33mW (2911) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]   -4  -31  260
[1]  -14  -22  262
[1]  -14  -24  261
[1]  -10  -22  262
[1]  -13  -23  270
[1]  -11  -28  264
[1]  -10  -26  259
[0;33mW (2931) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2931) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]   -7  -23  259
[1]  -11  -27  262
[1]  -15  -25  265
[1]  -12  -31  264
[0;33mW (2951) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[0;33mW (2951) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -22  -35  257
[1]   -8  -29  263
[2]  122  208  -16
[2]  134  224   -8
[0;33mW (2971) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  137  219  -17
[2]  138  210  -16
[2]  124  214  -27
[2]  124  212  -22
[2]  127  223  -13
[2]  126  208  -25
[0;33mW (2991) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (2991) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  125  223  -14
[2]  127  210  -20
[2]  121  209  -13
[0;33mW (3011) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3011) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  128  222  -19
[2]  122  206  -25
[2]  128  228   -7
[0;33mW (3031) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3031) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  124  212  -19
[2]  123  211  -28
[1]  -19  -18  252
[1]  -15  -23  274
[1]  -15  -23  262
[0;33mW (3051) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -11  -29  261
[1]   -6  -34  254
[1]  -12  -24  266
[1]  -13  -25  256
[1]  -23  -29  260
[1]  -15  -24  266
[0;33mW (3071) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3071) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -14  -27  267
[1]   -8  -26  255
[1]  -18  -18  259
[0;33mW (3091) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3091) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -18  -23  266
[1]  -22  -21  262
[1]  -12  -24  260
[1]  -13  -27  255
[0;33mW (3111) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  126  220  -15
[2]  131  227  -14
[2]  137  220   -9
[2]  132  226   -9
[2]  130  209  -19
[2]  122  211  -20
[2]  119  206  -28
[0;33mW (3131) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3131) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  129  217  -10
[2]  129  219  -23
[2]  126  211  -15
[0;33mW (3151) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3151) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  136  222  -21
[2]  120  205  -20
[2]  123  211  -17
[2]  121  207  -14
[0;33mW (3171) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3171) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  124  216  -12
[2]  124  208  -21
[1]   -9  -24  261
[1]   -9  -22  262
[1]  -21  -31  263
[0;33mW (3191) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -11  -26  265
[1]  -17  -28  262
[1]  -15  -21  264
[1]  -15  -26  263
[1]   -6  -26  265
[1]  -10  -18  262
[0;33mW (3211) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3211) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -10  -17  257
[1]  -11  -26  260
[1]  -14  -30  260
[0;33mW (3231) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3231) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -11  -25  255
[1]  -16  -28  260
[1]  -14  -25  258
[0;33mW (3251) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3251) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -13  -28  269
[2]  126  222  -19
[2]  126  216  -13
[2]  130  213  -16
[2]  133  218  -14
[0;33mW (3271) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  130  218  -11
[2]  136  220  -20
[2]  125  214   -7
[2]  135  217  -17
[2]  118  206  -21
[2]  136  224  -18
[0;33mW (3291) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3291) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  125  205  -21
[2]  129  219  -23
[2]  128  216  -21
[0;33mW (3311) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3311) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  126  231   -3
[2]  128  222   -3
[2]  126  209  -20
[0;33mW (3331) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -18  -24  261
[1]  -15  -17  270
[1]  -11  -22  257
[1]  -10  -21  274
[1]  -11  -16  264
[1]   -8  -21  260
[1]  -10  -23  253
[1]  -11  -26  267
[0;33mW (3351) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3361) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -18  -22  256
[1]  -11  -27  260
[1]  -16  -22  263
[0;33mW (3371) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3371) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]   -8  -27  262
[1]  -14  -26  259
[1]  -12  -27  267
[1]  -19  -26  269
[0;33mW (3391) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3401) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -14  -20  261
[0;32mI (3411) DUAL_ADXL345: Samples per second: 224/s[0m
[0;33mW (3411) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[2]  124  205  -22
[2]  128  220  -15
[2]  122  205  -20
[2]  126  214  -12
[2]  135  205  -15
[2]  133  223   -6
[0;33mW (3431) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3441) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  125  206  -23
[2]  118  205  -20
[2]  131  221  -11
[0;33mW (3451) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3461) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  124  208  -24
[2]  129  220   -4
[2]  124  208  -23
[2]  118  211  -15
[0;33mW (3471) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3481) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[2]  124  208  -23
[2]  126  226  -10
[2]  128  215  -15
[0;33mW (3491) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[1]  -16  -17  260
[1]  -12  -26  247
[1]  -13  -24  258
[1]   -6  -29  261
[1]   -7  -20  261
[1]  -16  -19  261
[1]  -12  -28  254
[1]  -12  -28  262
[0;33mW (3511) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3521) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -16  -29  270
[1]  -14  -26  258
[1]  -10  -24  258
[0;33mW (3531) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3541) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]   -8  -23  261
[1]  -12  -26  259
[1]  -18  -29  260
[0;33mW (3551) DUAL_ADXL345: Sensor2_Bus1: Data queue full, dropping samples[0m
[0;33mW (3561) DUAL_ADXL345: Sensor1_Bus0: Data queue full, dropping samples[0m
[1]  -11  -25  265
[1]  -15  -25  263
[2]  131  224  -12
[2]  130  225  -14
