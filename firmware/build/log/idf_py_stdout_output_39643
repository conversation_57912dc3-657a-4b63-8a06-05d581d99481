DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bce4h ( 48356) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bd0c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e7a8 vaddr=40374000 size=01870h (  6256) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a544h (107844) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a56c vaddr=40375870 size=0df70h ( 57200) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (197) app_init: App version:      f08ec8f-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:21:18[0m
[0;32mI (208) app_init: ELF file SHA256:  e059babe5...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (273) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (441) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (441) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (451) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (451) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (461) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (461) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (461) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (471) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (471) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (481) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (491) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (491) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (491) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (501) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (521) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (541) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (1321) DUAL_ADXL345: Samples per second: 672/s[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (2361) DUAL_ADXL345: Samples per second: 816/s[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (3381) DUAL_ADXL345: Samples per second: 816/s[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (4391) DUAL_ADXL345: Samples per second: 800/s[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (5431) DUAL_ADXL345: Samples per second: 816/s[0m
DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345DUAL_ADXL345[0;32mI (6441) DUAL_ADXL345: Samples per second: 800/s[0m
