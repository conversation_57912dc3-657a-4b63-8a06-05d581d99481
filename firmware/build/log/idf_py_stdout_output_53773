ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x4200c1e0
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc40
load:0x403cb700,len:0x2ee8
entry 0x403c8908
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 17:01:59[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v0.2[0m
[0;32mI (27) boot: efuse block revision: v1.3[0m
[0;32mI (27) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (28) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (28) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (28) boot: Enabling RNG early entropy source...[0m
[0;32mI (28) boot: Partition Table:[0m
[0;32mI (28) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (29) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (29) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (30) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (30) boot: End of partition table[0m
[0;32mI (30) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0c154h ( 49492) map[0m
[0;32mI (40) esp_image: segment 1: paddr=0001c17c vaddr=3fc93800 size=02a14h ( 10772) load[0m
[0;32mI (42) esp_image: segment 2: paddr=0001eb98 vaddr=40374000 size=01480h (  5248) load[0m
[0;32mI (44) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1af24h (110372) map[0m
[0;32mI (64) esp_image: segment 4: paddr=0003af4c vaddr=40375480 size=0e344h ( 58180) load[0m
[0;32mI (83) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (83) boot: Disabling RNG early entropy source...[0m
[0;32mI (84) cpu_start: Multicore app[0m
[0;32mI (93) cpu_start: Pro cpu start user code[0m
[0;32mI (93) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (94) app_init: Application information:[0m
[0;32mI (94) app_init: Project name:     firmware[0m
[0;32mI (94) app_init: App version:      a6eb462-dirty[0m
[0;32mI (94) app_init: Compile time:     May 27 2025 17:02:04[0m
[0;32mI (94) app_init: ELF file SHA256:  5fea1025f...[0m
[0;32mI (95) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (95) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (95) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (95) efuse_init: Chip rev:         v0.2[0m
[0;32mI (95) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (96) heap_init: At 3FC96AD0 len 00052C40 (331 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (96) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (97) spi_flash: detected chip: gd[0m
[0;32mI (98) spi_flash: flash io: dio[0m
[0;33mW (98) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (98) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (99) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (99) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (100) main_task: Started on CPU0[0m
[0;32mI (110) main_task: Calling app_main()[0m
[0;32mI (110) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (210) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (210) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (220) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (220) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (230) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (230) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (230) DUAL_ADXL345: UART initialized: 921600 baud, TX=43, RX=44[0m
[0;32mI (230) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (230) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (240) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (240) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (240) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;32mI (1110) DUAL_ADXL345: Samples per second: 1424/s[0m
[0;32mI (2120) DUAL_ADXL345: Samples per second: 1600/s[0m
