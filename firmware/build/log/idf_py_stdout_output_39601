[1/5] cd /Users/<USER>/Workspace/master/firmware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/Workspace/master/firmware/build/partition_table/partition-table.bin /Users/<USER>/Workspace/master/firmware/build/firmware.bin
firmware.bin binary size 0x38500 bytes. Smallest app partition is 0x100000 bytes. 0xc7b00 bytes (78%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/Workspace/master/firmware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/Workspace/master/firmware/build/bootloader/bootloader.bin
Bootloader binary size 0x5490 bytes. 0x2b70 bytes (34%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/.espressif/esp-idf/v5.3.2 -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python;;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/Workspace/master/firmware/build -P /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32s3 -p /dev/cu.usbserial-1420 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 firmware.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port /dev/cu.usbserial-1420
Connecting...
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE
Crystal is 40MHz
MAC: 74:4d:bd:aa:bd:e8
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x00048fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21648 bytes to 13306...
Writing at 0x00000000... (100 %)
Wrote 21648 bytes (13306 compressed) at 0x00000000 in 0.6 seconds (effective 290.1 kbit/s)...
Hash of data verified.
Compressed 230656 bytes to 119884...
Writing at 0x00010000... (12 %)
Writing at 0x0001d4dc... (25 %)
Writing at 0x00023c58... (37 %)
Writing at 0x0002aa69... (50 %)
Writing at 0x00031344... (62 %)
Writing at 0x00039e64... (75 %)
Writing at 0x0003fb9d... (87 %)
Writing at 0x000461ad... (100 %)
Wrote 230656 bytes (119884 compressed) at 0x00010000 in 3.1 seconds (effective 597.7 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 103...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (103 compressed) at 0x00008000 in 0.1 seconds (effective 327.7 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
