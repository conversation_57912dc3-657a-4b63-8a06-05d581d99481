[1/9] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/Workspace/master/firmware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/Workspace/master/firmware/build/bootloader/bootloader.bin
Bootloader binary size 0x5490 bytes. 0x2b70 bytes (34%) free.
[2/9] No install step for 'bootloader'
[3/9] Completed 'bootloader'
[4/9] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj
[5/9] Linking C static library esp-idf/main/libmain.a
[6/9] Generating ld/sections.ld
[7/9] Linking CXX executable firmware.elf
[8/9] Generating binary image from built executable
esptool.py v4.8.1
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /Users/<USER>/Workspace/master/firmware/build/firmware.bin
[9/9] cd /Users/<USER>/Workspace/master/firmware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/Workspace/master/firmware/build/partition_table/partition-table.bin /Users/<USER>/Workspace/master/firmware/build/firmware.bin
firmware.bin binary size 0x38500 bytes. Smallest app partition is 0x100000 bytes. 0xc7b00 bytes (78%) free.
