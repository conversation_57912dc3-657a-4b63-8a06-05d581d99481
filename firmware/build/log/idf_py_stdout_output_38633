ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bc04h ( 48132) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bc2c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e6c8 vaddr=40374000 size=01950h (  6480) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a450h (107600) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a478 vaddr=40375950 size=0de90h ( 56976) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      f08ec8f-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:21:18[0m
[0;32mI (208) app_init: ELF file SHA256:  35a9ef82b...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (272) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=17, SCL=15)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=13)[0m
[0;32mI (341) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (341) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;31mE (351) DUAL_ADXL345: No ADXL345 found on Sensor1_Bus0 (DEVID=0x00)[0m
[0;31mE (361) DUAL_ADXL345: Failed to initialize Sensor1_Bus0, terminating task[0m
[0;32mI (361) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;31mE (371) DUAL_ADXL345: No ADXL345 found on Sensor2_Bus1 (DEVID=0x00)[0m
[0;31mE (381) DUAL_ADXL345: Failed to initialize Sensor2_Bus1, terminating task[0m
[0;32mI (371) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (391) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=17, SCL=15), Address=0x53[0m
[0;32mI (401) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=13), Address=0x53[0m
