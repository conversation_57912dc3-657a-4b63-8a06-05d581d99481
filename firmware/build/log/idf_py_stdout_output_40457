[0]    -12    -26    260
[1]    -10    -24    256
[2]    -16    -24    264
[0;33mW (791) DUAL_ADXL345: Sensor2_Bus1: Failed to read sampl�ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bce4h ( 48356) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bd0c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e7a8 vaddr=40374000 size=01870h (  6256) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a540h (107840) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a568 vaddr=40375870 size=0df70h ( 57200) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (197) app_init: App version:      f08ec8f-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:21:18[0m
[0;32mI (208) app_init: ELF file SHA256:  0a5b7ff2c...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (273) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (441) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (441) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (451) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (451) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (461) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (461) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (461) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (471) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (471) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (481) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (491) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (491) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0]    -14    -25    260
[0;32mI (501) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[1]    -13    -26    263
[2]    -15    -26    257
[0]    -13    -25    263
[1]    -14    -26    262
[2]    -15    -26    261
[0;32mI (491) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (531) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (541) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0]    -13    -27    260
[1]     -8    -20    264
[2]    -13    -21    265
[0]    120    199    -14
[1]    127    214    -17
[2]    128    215    -16
[0]    -13    -23    262
[1]    -13    -25    261
[2]     -9    -25    263
[0]    120    208    -22
[1]    130    209    -19
[2]    124    210    -26
[0]    -12    -26    261
[1]    -15    -15    262
[2]    -15    -30    276
[0]    133    223    -11
[1]    130    210    -18
[2]    125    209    -21
[0]    -12    -26    260
[1]    -12    -25    259
[2]    -12    -20    269
[0]    125    212    -23
[1]    130    219     -7
[2]    132    214    -22
[0]    -13    -20    255
[1]     -8    -23    265
[2]    -13    -24    264
[0]    132    222     -8
[1]    124    213    -16
[2]    127    214    -12
[0]    -15    -31    265
[1]    -17    -27    268
[2]    -14    -27    263
[0]    128    215    -21
[1]    127    215    -11
[2]    130    211     -8
[0]     -9    -26    260
[1]     -8    -22    256
[2]     -3    -20    259
[0]    128    208    -21
[1]    132    213    -20
[2]    130    208    -22
[0]    -14    -25    265
[1]    -15    -29    265
[2]    -15    -15    259
[0]    126    219    -10
[1]    131    207    -19
[2]    133    211    -13
[0]    -16    -20    261
[1]    -12    -22    260
[2]    -16    -19    257
[0]    133    215    -21
[1]    133    216    -11
[2]    130    220    -11
[0]    -14    -29    262
[1]    -17    -24    260
[2]    -18    -22    265
[0]    129    219    -13
[1]    125    212    -16
[2]    127    216    -20
[0]    -18    -27    263
[1]    -14    -25    262
[2]    -17    -31    266
[0]    121    214    -17
[1]    124    215    -19
[2]    125    211    -19
[0]    -13    -25    261
[1]    -14    -18    264
[2]    -10    -28    269
[0]    135    211    -19
[1]    134    216    -22
[2]    129    212    -20
[0]    -12    -28    261
[1]    -13    -23    267
[2]    -17    -27    267
[0]    126    209    -15
[1]    126    213    -17
[2]    124    212    -24
[0]    -12    -29    262
[1]    -14    -27    261
[2]    -11    -21    264
[0]    136    216    -16
[1]    129    218    -13
[2]    125    207    -20
[0]    -16    -29    258
[1]    -12    -26    256
[2]    -17    -22    263
[0]    125    213    -18
[1]    127    214    -22
[2]    126    207    -20
[0]    -11    -22    259
[1]     -8    -23    261
[2]    -16    -29    258
[0]    125    212    -12
[1]    126    214    -19
[2]    122    211    -18
[0]    -12    -26    267
[1]    -16    -26    259
[2]    -14    -29    279
[0]    137    222    -12
[1]    133    216    -13
[2]    127    216     -7
[0]    -17    -27    262
[1]    -11    -23    259
[2]    -13    -28    261
[0]    125    208    -22
[1]    125    212    -19
[2]    128    212    -22
[0]    -13    -29    260
[1]    -17    -27    262
[2]    -11    -31    264
[0]    132    213    -12
[1]    129    217     -8
[2]    128    219    -19
[0]    -17    -21    263
[1]    -15    -28    262
[2]    -10    -24    268
[0]    126    213    -18
[1]    128    211    -22
[2]    125    209    -19
[0;32mI (1321) DUAL_ADXL345: Samples per second: 672/s[0m
[0]    -12    -28    260
[1]    -13    -24    261
[2]    -11    -27    265
[0]    131    218    -11
[1]    133    219    -10
[2]    136    215    -15
[0]    -17    -25    267
[1]    -12    -26    262
[2]    -16    -22    260
[0]    123    223    -11
[1]    129    217    -15
[2]    133    213    -13
[0]    -12    -26    260
[1]    -13    -27    263
[2]    -14    -18    262
[0]    130    216    -13
[1]    131    220    -18
[2]    130    219    -14
[0]    -14    -27    261
[1]    -15    -26    266
[2]    -14    -17    258
[0]    132    218    -14
[1]    132    224    -13
[2]    134    218    -13
[0]    -13    -27    257
[1]    -10    -21    262
[2]    -15    -26    263
[0]    131    222     -8
[1]    128    220    -12
[2]    134    215    -12
[0]    -15    -34    261
[1]    -16    -26    265
[2]    -10    -20    266
[0]    125    209    -27
[1]    133    216    -18
[2]    128    216    -21
[0]    -17    -24    258
[1]    -11    -27    258
[2]    -12    -23    262
[0]    133    217    -12
[1]    130    214    -11
[2]    125    222    -17
[0]    -13    -27    260
[1]     -9    -23    265
[2]    -14    -21    258
[0]    127    217    -16
[1]    126    220    -10
[2]    126    214    -19
[0]    -15    -24    264
[1]    -14    -25    265
[2]     -8    -26    265
[0]    126    211    -16
[1]    131    217    -20
[2]    126    212    -26
[0]    -13    -24    261
[1]     -8    -33    262
[2]    -12    -26    260
[0]    136    218    -12
[1]    128    213    -15
[2]    125    210    -19
[0]    -15    -23    263
[1]    -13    -24    264
[2]    -14    -27    263
[0]    125    210    -13
[1]    129    216    -15
[2]    129    218    -10
[0]    -11    -30    265
[1]    -18    -20    262
[2]    -15    -17    264
[0]    136    214    -12
[1]    128    215    -15
[2]    127    214    -17
[0]    -17    -28    259
[1]     -9    -28    264
[2]    -13    -27    269
[0]    128    218    -13
[1]    132    216    -13
[2]    134    214    -10
[0]    -11    -23    266
[1]    -18    -24    262
[2]     -5    -26    267
[0]    123    214    -17
[1]    128    217    -13
[2]    123    215    -20
[0]    -15    -24    259
[1]     -8    -24    262
[2]    -10    -22    265
[0]    130    210    -20
[1]    134    220    -16
[2]    131    217    -16
[0]    -14    -28    263
[1]    -19    -23    264
[2]    -10    -24    266
[0]    126    208    -24
[1]    131    217    -13
[2]    130    215    -15
[0]    -14    -28    260
[1]    -11    -25    265
[2]    -13    -26    261
[0]    128    217    -16
[1]    130    219    -15
[2]    132    215    -13
[0]    -13    -26    261
[1]    -11    -21    265
[2]    -20    -21    262
[0]    121    206    -24
[1]    129    215    -11
[2]    130    213     -7
[0]    -16    -21    257
[1]    -14    -26    264
[2]    -10    -26    263
[0]    128    211    -18
[1]    130    216    -11
[2]    128    209    -18
[0]    -14    -24    265
[1]    -17    -25    263
[2]    -18    -17    263
[0]    124    215    -16
[1]    129    213    -20
[2]    128    211    -13
[0]    -12    -27    264
[1]    -11    -25    264
[2]    -18    -30    264
[0]    127    211    -24
[1]    129    217    -16
[2]    131    221    -11
[0]    -10    -26    261
[1]    -14    -23    261
[2]    -12    -22    257
[0]    130    223    -17
[1]    131    216    -14
[2]    130    219    -19
[0]    -17    -22    257
[1]    -10    -20    264
[2]    -15    -24    262
[0]    130    216    -14
[1]    131    215    -16
[2]    131    216    -17
[0]    -14    -27    263
[1]    -18    -26    262
[2]     -8    -25    265
[0]    133    217    -20
[1]    127    220    -13
[2]    128    210    -20
[0]    -15    -25    263
[1]     -9    -26    261
[2]    -13    -29    262
[0;32mI (2331) DUAL_ADXL345: Samples per second: 784/s[0m
[0]    130    212    -19
[1]    128    211    -21
[2]    122    216    -13
[0]    -14    -22    261
[1]    -14    -25    263
[2]    -10    -27    260
[0]    129    213    -18
[1]    134    216    -20
[2]    129    216    -22
[0]    -12    -27    259
[1]    -11    -26    264
[2]    -11    -28    265
[0]    133    217    -19
[1]    125    222    -17
[2]    125    219    -17
[0]    -15    -24    265
[1]    -13    -29    269
[2]    -11    -18    268
[0]    134    218    -17
[1]    130    213    -13
[2]    126    215    -13
[0]    -19    -22    264
[1]    -14    -24    260
[2]    -12    -31    259
[0]    125    218    -14
[1]    136    218    -18
[2]    128    211    -19
[0]    -13    -31    260
[1]    -13    -31    261
[2]    -11    -23    258
[0]    129    223    -10
[1]    128    214    -14
[2]    127    216    -20
[0]    -13    -22    261
[1]    -11    -27    261
[2]    -12    -20    262
[0]    125    208    -16
[1]    131    218    -20
[2]    130    220    -17
[0]    -17    -23    261
[1]    -12    -26    265
[2]    -13    -26    259
[0]    131    217    -19
[1]    135    218    -14
[2]    134    216    -18
[0]    -12    -27    261
[1]    -11    -26    264
[2]    -16    -21    262
[0]    130    222    -12
[1]    134    215    -13
[2]    136    209    -17
[0]    -16    -29    266
[1]    -10    -24    264
[2]    -16    -25    260
[0]    128    212    -22
[1]    131    219    -18
[2]    128    215    -12
[0]    -13    -25    263
[1]    -18    -21    259
[2]    -14    -25    261
[0]    122    214    -27
[1]    133    211    -18
[2]    136    216    -14
[0]    -12    -29    260
[1]    -14    -30    260
[2]    -12    -24    267
[0]    131    216    -20
[1]    133    218    -11
[2]    133    222    -13
[0]    -17    -21    258
[1]    -13    -25    266
[2]    -16    -30    262
[0]    128    217     -9
[1]    125    215    -19
[2]    128    212    -20
[0]    -13    -26    262
[1]    -15    -22    262
[2]    -12    -22    260
[0]    129    211    -21
[1]    130    215    -17
[2]    126    219    -21
[0]    125    205    -24
[1]    132    215    -15
[2]    131    218    -22
[0]    -15    -27    265
[1]    -18    -23    263
[2]    -17    -26    264
[0]    123    213    -15
[1]    132    218    -19
[2]    130    212    -18
[0]    -16    -26    258
[1]    -17    -26    268
[2]    -12    -28    264
[0]    123    207    -19
[1]    135    219    -16
[2]    131    219    -17
[0]    -19    -22    261
[1]    -14    -28    266
[2]    -10    -22    264
[0]    129    215    -14
[1]    125    212    -21
[2]    126    210    -17
[0]    -13    -29    264
[1]    -15    -26    263
[2]    -14    -28    260
[0]    125    213    -12
[1]    129    213    -19
[2]    125    216    -11
[0]    -13    -28    260
[1]    -14    -26    265
[2]     -8    -26    263
[0]    122    208    -22
[1]    129    213    -21
[2]    127    210    -25
[0]    -16    -31    263
[1]    -17    -23    259
[2]     -9    -23    264
[0]    126    215    -12
[1]    127    215    -18
[2]    127    218     -8
[0]    -15    -29    261
[1]    -14    -26    271
[2]    -11    -28    263
[0]    127    208    -24
[1]    129    211    -17
[2]    124    209    -16
[0]    -16    -23    265
[1]    -20    -22    270
[2]     -8    -25    264
[0]    129    213    -17
[1]    130    210    -16
[2]    126    210    -16
[0]    -13    -27    264
[1]    -12    -26    262
[2]    -10    -25    262
[0]    124    207    -27
[1]    127    211    -21
[2]    128    208    -20
[0]    -19    -24    261
[1]    -11    -24    266
[2]    -12    -26    263
[0]    127    212    -27
[1]    127    217    -21
[2]    125    213    -21
[0]    -14    -29    269
[1]    -20    -21    257
[2]    -11    -25    264
[0]    125    213    -15
[1]    129    211    -13
[2]    128    213    -21
[0;32mI (3341) DUAL_ADXL345: Samples per second: 800/s[0m
[0]    -17    -29    259
[1]    -16    -29    266
[2]    -19    -27    260
[0]    129    226    -12
[1]    130    217     -9
[2]    131    215    -14
[0]    -18    -23    264
[1]    -15    -24    264
[2]    -12    -24    260
[0]    131    223    -12
[1]    133    215    -14
[2]    132    216    -14
[0]    -12    -28    263
[1]    -15    -16    265
[2]    -11    -24    259
[0]    131    222    -11
[1]    130    215    -18
[2]    125    209    -25
[0]    -15    -26    264
[1]     -5    -26    260
[2]    -15    -31    267
[0]    128    221    -18
[1]    130    222    -18
[2]    128    213    -11
[0]    -12    -22    261
[1]    -11    -25    265
[2]    -19    -22    261
[0]    126    217    -13
[1]    126    215    -16
[2]    129    213    -19
[0]    -11    -31    262
[1]    -14    -25    263
[2]    -15    -22    265
[0]    134    221    -13
[1]    134    217    -10
[2]    126    218    -16
[0]    -13    -28    257
[1]    -15    -19    261
[2]     -9    -24    260
[0]    129    225     -7
[1]    131    211    -13
[2]    130    221    -13
[0]    -14    -25    265
[1]    -13    -24    261
[2]    -11    -26    265
[0]    124    211    -11
[1]    131    214    -17
[2]    132    217    -23
[0]    -17    -26    255
[1]    -21    -21    270
[2]    -10    -26    262
[0]    123    211    -17
[1]    125    217    -25
[2]    126    214    -17
[0]    -15    -24    263
[1]    -17    -27    257
[2]    -11    -24    262
[0]    125    215     -8
[1]    126    210    -19
[2]    125    211    -21
[0]    -12    -29    263
[1]     -9    -19    269
[2]    -13    -24    261
[0]    130    211    -17
[1]    135    220    -14
[2]    133    213    -16
[0]    -11    -24    265
[1]    -13    -23    268
[2]    -15    -26    261
[0]    129    218     -8
[1]    136    213    -17
[2]    130    210    -14
[0]    -11    -25    260
[1]     -7    -28    266
[2]    -20    -24    263
[0]    128    224     -4
[1]    127    220     -6
[2]    134    218    -16
[0]    -12    -27    261
[1]    -14    -22    267
[2]    -18    -21    260
[0]    128    229    -13
[1]    133    218    -10
[2]    131    217    -10
[0]    -13    -27    266
[1]    -15    -29    257
[2]    -17    -22    261
[0]    124    210    -23
[1]    136    214    -23
[2]    124    209    -20
[0]    -15    -32    259
[1]     -9    -29    261
[2]    -10    -26    253
[0]    124    210     -8
[1]    130    211    -13
[2]    129    210    -18
[0]    -15    -25    265
