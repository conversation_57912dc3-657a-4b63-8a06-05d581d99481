[0;32mI (267) spi_flash: flash iESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40048839
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6f4h ( 46836) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001b71c vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e198 vaddr=40374000 size=01e80h (  7808) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a0c4h (106692) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a0ec vaddr=40375e80 size=0d960h ( 55648) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (188) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (191) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      3b2c6ea[0m
[0;32mI (201) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (207) app_init: ELF file SHA256:  3e675b7cb...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (217) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (222) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (227) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (239) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (251) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (285) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;32mI (319) adxl345: ADXL345 ±16g, FIFO stream @400Hz[0m
[0;32mI (329) adxl345: [ 0] X:   -32 Y:  -158 Z:   207[0m
[0;32mI (329) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (329) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (339) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (339) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (349) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (359) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (359) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (369) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (369) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (379) adxl345: [10] X:     0 Y:  2946 Z:   -30[0m
[0;32mI (379) adxl345: [11] X:  -162 Y:   205 Z:  7568[0m
[0;32mI (389) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (389) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (399) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (399) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (409) adxl345: [ 0] X:   -35 Y:  -156 Z:   204[0m
[0;32mI (409) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (419) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (419) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (429) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (429) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (439) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (449) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (449) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (459) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (459) adxl345: [10] X:     0 Y:  2946 Z:   -28[0m
[0;32mI (469) adxl345: [11] X:  -163 Y:   208 Z:  7824[0m
[0;32mI (469) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (479) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (479) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (489) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (499) adxl345: [ 0] X:   -33 Y:  -158 Z:   207[0m
[0;32mI (499) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (499) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (509) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (509) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (519) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (519) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (529) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (539) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (539) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (549) adxl345: [10] X:     0 Y:  2946 Z:   -34[0m
[0;32mI (549) adxl345: [11] X:  -158 Y:   209 Z:  7568[0m
[0;32mI (559) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (559) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (569) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (569) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (579) adxl345: [ 0] X:   -30 Y:  -164 Z:   210[0m
[0;32mI (579) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (589) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (589) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (599) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (599) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (609) adxl345: [ 6] X:  1786 Y:     0 Z: 12032[0m
[0;32mI (609) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (619) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (629) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (629) adxl345: [10] X:     0 Y:  2946 Z:   -31[0m
[0;32mI (639) adxl345: [11] X:  -161 Y:   209 Z:  7824[0m
[0;32mI (639) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (649) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (649) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (659) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (669) adxl345: [ 0] X:   -33 Y:  -157 Z:   208[0m
[0;32mI (669) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (669) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (679) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (679) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (689) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (689) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (699) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (699) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (709) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (719) adxl345: [10] X:     0 Y:  2946 Z:   -31[0m
[0;32mI (719) adxl345: [11] X:  -158 Y:   208 Z:  7568[0m
[0;32mI (729) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (729) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (739) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (739) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (749) adxl345: [ 0] X:   -39 Y:  -150 Z:   206[0m
[0;32mI (749) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (759) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (759) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (769) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (769) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (779) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (779) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (789) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (789) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (799) adxl345: [10] X:     0 Y:  2946 Z:   -33[0m
[0;32mI (809) adxl345: [11] X:  -159 Y:   207 Z:  7824[0m
[0;32mI (809) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (819) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (819) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (829) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (829) adxl345: [ 0] X:   -31 Y:  -157 Z:   207[0m
[0;32mI (839) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (839) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (849) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (849) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (859) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (859) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (869) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (869) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (879) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (879) adxl345: [10] X:     0 Y:  2946 Z:   -35[0m
[0;32mI (889) adxl345: [11] X:  -159 Y:   205 Z:  7568[0m
[0;32mI (899) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (899) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (909) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (909) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (919) adxl345: [ 0] X:   -31 Y:  -152 Z:   205[0m
[0;32mI (919) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (929) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (929) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (939) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (939) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (949) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (949) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (959) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (959) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (969) adxl345: [10] X:     0 Y:  2946 Z:   -34[0m
[0;32mI (969) adxl345: [11] X:  -159 Y:   209 Z:  7824[0m
[0;32mI (979) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (989) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (989) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (999) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (999) adxl345: [ 0] X:   -34 Y:  -159 Z:   210[0m
[0;32mI (1009) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1009) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1019) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1019) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1029) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (1029) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1039) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1039) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1049) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1059) adxl345: [10] X:     0 Y:  2946 Z:   -33[0m
[0;32mI (1059) adxl345: [11] X:  -158 Y:   210 Z:  7568[0m
[0;32mI (1069) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1069) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1079) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1079) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1089) adxl345: [ 0] X:   -32 Y:  -158 Z:   206[0m
[0;32mI (1089) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1099) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1099) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1109) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1109) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1119) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1119) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1129) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1139) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1139) adxl345: [10] X:     0 Y:  2946 Z:   -31[0m
[0;32mI (1149) adxl345: [11] X:  -161 Y:   207 Z:  7568[0m
[0;32mI (1149) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1159) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1159) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1169) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1179) adxl345: [ 0] X:   -32 Y:  -163 Z:   209[0m
[0;32mI (1179) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1179) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1189) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1189) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1199) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1209) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1209) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1219) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1219) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1229) adxl345: [10] X:     0 Y:  2946 Z:   -34[0m
[0;32mI (1229) adxl345: [11] X:  -158 Y:   209 Z:  7824[0m
[0;32mI (1239) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1239) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1249) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1249) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1259) adxl345: [ 0] X:   -34 Y:  -157 Z:   208[0m
[0;32mI (1259) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1269) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1279) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1279) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1289) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1289) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1299) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1299) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1309) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1309) adxl345: [10] X:     0 Y:  2946 Z:   -32[0m
[0;32mI (1319) adxl345: [11] X:  -160 Y:   208 Z:  7824[0m
[0;32mI (1319) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1329) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1329) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1339) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1349) adxl345: [ 0] X:   -34 Y:  -158 Z:   208[0m
[0;32mI (1349) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1359) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1359) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1369) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1369) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (1379) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1379) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1389) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1389) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1399) adxl345: [10] X:     0 Y:  2946 Z:   -31[0m
[0;32mI (1399) adxl345: [11] X:  -160 Y:   206 Z:  7824[0m
[0;32mI (1409) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1419) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1419) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1429) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1429) adxl345: [ 0] X:   -31 Y:  -159 Z:   207[0m
[0;32mI (1439) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1439) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1449) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1449) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1459) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1459) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1469) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1469) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1479) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1489) adxl345: [10] X:     0 Y:  2946 Z:   -32[0m
[0;32mI (1489) adxl345: [11] X:  -159 Y:   209 Z:  7824[0m
[0;32mI (1499) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1499) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1509) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1509) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1519) adxl345: [ 0] X:   -32 Y:  -159 Z:   211[0m
[0;32mI (1519) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1529) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1529) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1539) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1539) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1549) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1559) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1559) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1569) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1569) adxl345: [10] X:     0 Y:  2946 Z:   -31[0m
[0;32mI (1579) adxl345: [11] X:  -161 Y:   208 Z:  7824[0m
[0;32mI (1579) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1589) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1589) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1599) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1609) adxl345: [ 0] X:   -30 Y:  -158 Z:   204[0m
[0;32mI (1609) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1609) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1619) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1629) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1629) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1639) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1639) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1649) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1649) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1659) adxl345: [10] X:     0 Y:  2946 Z:   -30[0m
[0;32mI (1659) adxl345: [11] X:  -162 Y:   210 Z:  7824[0m
[0;32mI (1669) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1669) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1679) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1679) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1689) adxl345: [ 0] X:   -34 Y:  -157 Z:   206[0m
[0;32mI (1699) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1699) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1709) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1709) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1719) adxl345: [ 5] X:   128 Y:    48 Z:  -512[0m
[0;32mI (1719) adxl345: [ 6] X:  1787 Y:     0 Z: 12032[0m
[0;32mI (1729) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1729) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
