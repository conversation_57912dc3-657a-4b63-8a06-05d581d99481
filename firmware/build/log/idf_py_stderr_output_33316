[0;33m--- esp-idf-monitor 1.6.2 on /dev/cu.usbserial-1420 115200[0m
[0;33m--- Quit: Ctrl+] | Menu: Ctrl+T | Help: Ctrl+T followed by Ctrl+H[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0;33m--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
[0m
[0;33m--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
[0m
[0;33m--- 0x40375ade: panic_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c:463
--- 0x4037acdd: esp_system_abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c:92
--- 0x40381699: abort at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c:38
--- 0x4037acd3: _esp_error_check_failed at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c:49
--- 0x42008cc4: app_main at /Users/<USER>/Workspace/master/firmware/main/firmware.c:108 (discriminator 1)
--- 0x42019777: main_task at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c:208
--- 0x4037b785: vPortTaskWrapper at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c:139
[0m
[0;33m--- 0x40375a18: esp_restart_noos at /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c:158
[0m
[0m
