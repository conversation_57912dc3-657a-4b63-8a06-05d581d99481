ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x4037b8f0
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc40
load:0x403cb700,len:0x2ee8
entry 0x403c8908
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 17:01:59[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v0.2[0m
[0;32mI (27) boot: efuse block revision: v1.3[0m
[0;32mI (27) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (28) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (28) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (28) boot: Enabling RNG early entropy source...[0m
[0;32mI (28) boot: Partition Table:[0m
[0;32mI (28) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (29) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (29) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (30) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (30) boot: End of partition table[0m
[0;32mI (30) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0c254h ( 49748) map[0m
[0;32mI (40) esp_image: segment 1: paddr=0001c27c vaddr=3fc93800 size=02a14h ( 10772) load[0m
[0;32mI (42) esp_image: segment 2: paddr=0001ec98 vaddr=40374000 size=01380h (  4992) load[0m
[0;32mI (44) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1b13ch (110908) map[0m
[0;32mI (64) esp_image: segment 4: paddr=0003b164 vaddr=40375380 size=0e444h ( 58436) load[0m
[0;32mI (83) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (83) boot: Disabling RNG early entropy source...[0m
[0;32mI (84) cpu_start: Multicore app[0m
[0;32mI (93) cpu_start: Pro cpu start user code[0m
[0;32mI (93) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (94) app_init: Application information:[0m
[0;32mI (94) app_init: Project name:     firmware[0m
[0;32mI (94) app_init: App version:      7870de4-dirty[0m
[0;32mI (94) app_init: Compile time:     May 27 2025 17:21:13[0m
[0;32mI (95) app_init: ELF file SHA256:  fa1d617ad...[0m
[0;32mI (95) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (95) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (95) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (95) efuse_init: Chip rev:         v0.2[0m
[0;32mI (95) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (96) heap_init: At 3FC96AD0 len 00052C40 (331 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (97) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (98) spi_flash: detected chip: gd[0m
[0;32mI (98) spi_flash: flash io: dio[0m
[0;33mW (98) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (98) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (99) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (99) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (100) main_task: Started on CPU0[0m
[0;32mI (110) main_task: Calling app_main()[0m
[0;32mI (110) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (210) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (210) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (220) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (220) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (230) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (230) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (230) DUAL_ADXL345: UART initialized: 460800 baud, TX=43, RX=44[0m
[0;32mI (230) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (230) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (240) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (240) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (240) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;32mI (1110) DUAL_ADXL345: Samples per second: 1424/s[0m
[0;32mI (2120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (3130) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (4140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (5160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (6170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (7180) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (8190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (9200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (10210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (10240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (11220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (12230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (13240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (14250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (15270) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (16280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (17290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (18300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (19320) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (20240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (20330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (21340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (22350) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (23360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (24370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (25380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (26390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (27410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (28420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (29430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (30240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (30440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (31450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (32460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (33470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (34480) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (35500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (36510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (37520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (38530) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (39540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (40240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (40550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (41560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (42570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (43580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (44590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (45600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (46620) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (47630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (48640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (49650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (50240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (50670) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (51680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (52690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (53700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (54710) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (55720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (56730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (57740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (58760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (59770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (60240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (60780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (61790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (62800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (63810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (64820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (65830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (66850) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (67860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (68870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (69880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (70240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (70890) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (71900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (72910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (73930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (74940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (75950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (76960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (77970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (78980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (79990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (80240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (81000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (82010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (83030) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (84040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (85050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (86060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (87070) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (88090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (89100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (90110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (90240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (91120) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (92130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (93140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (94150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (95160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (96170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (97180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (98190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (99210) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 1)[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 2)[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 3)[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Giving up on sample 0 after 4 retries[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Only got 0/16 samples, skipping this burst[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 1)[0m
[0;33mW (100020) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 2)[0m
[0;33mW (100030) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0 (retry 3)[0m
[0;33mW (100030) DUAL_ADXL345: Sensor2_Bus1: Giving up on sample 0 after 4 retries[0m
[0;33mW (100030) DUAL_ADXL345: Sensor2_Bus1: Only got 0/16 samples, skipping this burst[0m
[0;32mI (100220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (100240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (101230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (102240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (103260) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (104270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (105280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (106290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (107300) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (108310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (109320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (110240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (110330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (111340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (112350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (113360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (114370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (115390) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (116400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (117410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (118420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (119440) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (120240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (120450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (121460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (122470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (123480) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (124490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (125500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (126520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (127530) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (128540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (129550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (130240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (130560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (131570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (132580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (133590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (134600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (135620) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (136630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (137640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (138650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (139660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (140240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (140670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (141680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (142700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (143710) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (144720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (145730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (146740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (147750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (148760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (149770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (150240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (150780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (151800) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (152810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (153820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (154830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (155840) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (156860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (157870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (158880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (159890) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (160240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (160900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (161910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (162920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (163930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (164940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (165950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (166960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (167980) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (168990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (170000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (170240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (171010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (172020) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (173040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (174050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (175060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (176070) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (177080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (178090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (179100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (180110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (180240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (181120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (182130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (183140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (184160) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (185170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (186180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (187190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (188210) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (189220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (190230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (190240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (191240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (192250) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (193260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (194270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (195280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (196300) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (197310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (198320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (199330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (200240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (200340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (201350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (202360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (203370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (204390) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (205400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (206410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (207420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (208430) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (209440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (210240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (210450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (211470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (212480) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (213490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (214500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (215510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (216520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (217530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (218540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (219550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (220240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (220570) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (221580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (222590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (223600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (224610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (225620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (226640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (227650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (228660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (229670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (230240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (230680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (231690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (232700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (233730) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;32mI (234740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (235750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (236770) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (237780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (238790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (239800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (240240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (240810) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (241830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (242840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (243850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (244860) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (245870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (246880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (247890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (248900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (249920) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (250240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (250930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (251940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (252950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (253960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (254970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (255980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (256990) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (258010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (259020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (260030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (260240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (261040) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (262050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (263060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (264070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (265080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (266090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (267100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (268110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (269130) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (270140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (270240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (271150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (272160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (273170) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (274190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (275200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (276210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (277220) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (278230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (279240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (280240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (280250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (281260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (282270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (283280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (284290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (285310) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (286320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (287330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (288340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (289360) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (290240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (290370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (291380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (292390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (293400) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (294410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (295420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (296430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (297440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (298460) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (299470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (300240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (300480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (301490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (302500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (303510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (304520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (305540) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (306550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (307560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (308570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (309580) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (310240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (310590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (311600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (312620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (313630) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (314640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (315650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (316660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (317670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (318680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (319690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (320240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (320700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (321720) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (322730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (323740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (324750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (325760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (326770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (327780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (328800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (329810) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (330240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (330820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (331830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (332840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (333850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (334860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (335870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (336880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (337900) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (338910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (339920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (340240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (340930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (341940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (342950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (343960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (344980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (345990) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (347000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (348010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (349020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (350030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (350240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (351040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (352050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (353060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (354080) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (355090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (356100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (357110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (358120) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (359130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (360150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (360240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (361160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (362170) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (363180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (364190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (365200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (366210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (367220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (368230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (369240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (370240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (370260) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (371270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (372280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (373290) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (374300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (375320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (376330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (377340) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (378350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (379360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (380240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (380370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (381380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (382390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (383400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (384410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (385430) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (386440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (387450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (388460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (389470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (390240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (390490) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (391500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (392510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (393520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (394530) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (395540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (396550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (397570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (398580) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (399590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (400240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (400600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (401610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (402620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (403630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (404640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (405650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (406670) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (407680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (408690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (409700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (410240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (410710) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (411720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (412740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (413750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (414760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (415770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (416780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (417790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (418800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (419810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (420240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (420820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (421830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (422840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (423860) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (424870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (425880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (426890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (427910) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (428920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (429930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (430240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (430940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (431950) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (432960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (433980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (434990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (436000) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (437010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (438020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (439030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (440040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (440240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (441060) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (442070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (443080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (444090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (445100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (446110) DUAL_ADXL345: Samples per second: 1600/s[0m
