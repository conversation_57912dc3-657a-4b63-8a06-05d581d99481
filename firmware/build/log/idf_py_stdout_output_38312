[1/9] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj 
/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DESP_PLATFORM -DIDF_VER=\"v5.3.2\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -I/Users/<USER>/Workspace/master/firmware/build/config -I/Users/<USER>/Workspace/master/firmware/main -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/config/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/config/include/freertos -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/config/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/esp_additions/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/include/private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/include/apps -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/include/apps/sntp -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/freertos/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/esp32xx/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/esp32xx/include/arch -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/esp32xx/include/sys -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/mbedtls/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/mbedtls/library -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/esp_crt_bundle/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/mbedtls/3rdparty/everest/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/mbedtls/3rdparty/p256-m -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/i2c/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/touch_sensor/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/twai/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/touch_sensor/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/wifi_apps/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/wifi_apps/nan_app/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/include/local -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity/src -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock/CMock/src -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp-tls-crypto -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/interface -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/deprecated/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam/interface -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/interface -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/rgb/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c/protobuf-c -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/include/common -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/include/security -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/include/transports -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/include/crypto/srp6a -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/include/port/xtensa -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/diskio -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/src -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/vfs -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json/cJSON -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=/Users/<USER>/Workspace/master/firmware=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj -MF esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj -c /Users/<USER>/Workspace/master/firmware/main/firmware.c
/Users/<USER>/Workspace/master/firmware/main/firmware.c: In function 'i2c_master_init':
/Users/<USER>/Workspace/master/firmware/main/firmware.c:15:19: error: 'I2C_NUM_2' undeclared (first use in this function); did you mean 'I2C_NUM_1'?
   15 | #define I2C_BUS_1 I2C_NUM_2
      |                   ^~~~~~~~~
/Users/<USER>/Workspace/master/firmware/main/firmware.c:138:24: note: in expansion of macro 'I2C_BUS_1'
  138 |     err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
      |                        ^~~~~~~~~
/Users/<USER>/Workspace/master/firmware/main/firmware.c:15:19: note: each undeclared identifier is reported only once for each function it appears in
   15 | #define I2C_BUS_1 I2C_NUM_2
      |                   ^~~~~~~~~
/Users/<USER>/Workspace/master/firmware/main/firmware.c:138:24: note: in expansion of macro 'I2C_BUS_1'
  138 |     err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
      |                        ^~~~~~~~~
/Users/<USER>/Workspace/master/firmware/main/firmware.c: In function 'app_main':
/Users/<USER>/Workspace/master/firmware/main/firmware.c:15:19: error: 'I2C_NUM_2' undeclared (first use in this function); did you mean 'I2C_NUM_1'?
   15 | #define I2C_BUS_1 I2C_NUM_2
      |                   ^~~~~~~~~
/Users/<USER>/Workspace/master/firmware/main/firmware.c:301:21: note: in expansion of macro 'I2C_BUS_1'
  301 |         .i2c_port = I2C_BUS_1,
      |                     ^~~~~~~~~
[2/9] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/Workspace/master/firmware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/Workspace/master/firmware/build/bootloader/bootloader.bin
Bootloader binary size 0x5490 bytes. 0x2b70 bytes (34%) free.
ninja: build stopped: subcommand failed.
