[0;33mW (271) spi_flash: Detected size(8192k)ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40048839
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6b4h ( 46772) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001b6dc vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e158 vaddr=40374000 size=01ec0h (  7872) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a084h (106628) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a0ac vaddr=40375ec0 size=0d920h ( 55584) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (188) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (191) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      3b2c6ea[0m
[0;32mI (201) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (207) app_init: ELF file SHA256:  31f2e7f2f...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (217) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (222) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (227) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (239) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (251) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (285) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;32mI (319) adxl345: ADXL345 ±16g, FIFO stream @400Hz[0m
[0;32mI (329) adxl345: [ 0] X:   -15 Y:   249 Z:    24[0m
[0;32mI (329) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (329) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (339) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (339) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (349) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (359) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (359) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (369) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (369) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (379) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (379) adxl345: [11] X:   250 Y:    20 Z:  7568[0m
[0;32mI (389) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (389) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (399) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (399) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (409) adxl345: [ 0] X:   -16 Y:   244 Z:    18[0m
[0;32mI (409) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (419) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (419) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (429) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (429) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (439) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (449) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (449) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (459) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (459) adxl345: [10] X:     0 Y:  2946 Z:    -6[0m
[0;32mI (469) adxl345: [11] X:   250 Y:    22 Z:  7824[0m
[0;32mI (469) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (479) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (479) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (489) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (499) adxl345: [ 0] X:   -11 Y:   253 Z:    21[0m
[0;32mI (499) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (499) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (509) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (509) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (519) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (519) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (529) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (539) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (539) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (549) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (549) adxl345: [11] X:   252 Y:    22 Z:  7824[0m
[0;32mI (559) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (559) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (569) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (569) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (579) adxl345: [ 0] X:   -12 Y:   248 Z:    22[0m
[0;32mI (579) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (589) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (589) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (599) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (599) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (609) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (609) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (619) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (629) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (629) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (639) adxl345: [11] X:   252 Y:    25 Z:  7568[0m
[0;32mI (639) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (649) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (649) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (659) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (669) adxl345: [ 0] X:   -15 Y:   255 Z:    14[0m
[0;32mI (669) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (669) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (679) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (679) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (689) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (689) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (699) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (699) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (709) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (719) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (719) adxl345: [11] X:   252 Y:    23 Z:  7824[0m
[0;32mI (729) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (729) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (739) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (739) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (749) adxl345: [ 0] X:   -11 Y:   248 Z:    24[0m
[0;32mI (749) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (759) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (759) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (769) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (769) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (779) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (779) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (789) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (789) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (799) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (809) adxl345: [11] X:   252 Y:    24 Z:  7824[0m
[0;32mI (809) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (819) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (819) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (829) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (829) adxl345: [ 0] X:   -12 Y:   251 Z:    20[0m
[0;32mI (839) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (839) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (849) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (849) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (859) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (859) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (869) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (869) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (879) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (879) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (889) adxl345: [11] X:   250 Y:    20 Z:  7568[0m
[0;32mI (899) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (899) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (909) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (909) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (919) adxl345: [ 0] X:    -9 Y:   250 Z:    25[0m
[0;32mI (919) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (929) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (929) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (939) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (939) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (949) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (949) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (959) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (959) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (969) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (969) adxl345: [11] X:   251 Y:    20 Z:  7824[0m
[0;32mI (979) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (989) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (989) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (999) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (999) adxl345: [ 0] X:   -11 Y:   249 Z:    26[0m
[0;32mI (1009) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1009) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1019) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1019) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1029) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1029) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1039) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1039) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1049) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1059) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (1059) adxl345: [11] X:   248 Y:    25 Z:  7824[0m
[0;32mI (1069) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1069) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1079) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1079) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1089) adxl345: [ 0] X:   -13 Y:   249 Z:    21[0m
[0;32mI (1089) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1099) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1099) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1109) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1109) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1119) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1129) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1129) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1139) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1139) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (1149) adxl345: [11] X:   250 Y:    25 Z:  7824[0m
[0;32mI (1149) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1159) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1159) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1169) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1179) adxl345: [ 0] X:   -14 Y:   252 Z:    21[0m
[0;32mI (1179) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1179) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1189) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1189) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1199) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1209) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1209) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1219) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1219) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1229) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (1229) adxl345: [11] X:   249 Y:    23 Z:  7824[0m
[0;32mI (1239) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1239) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1249) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1249) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1259) adxl345: [ 0] X:   -12 Y:   252 Z:    17[0m
[0;32mI (1259) adxl345: [ 1] X:  8336 Y:     0 Z:     0[0m
[0;32mI (1269) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1279) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1279) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1289) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1289) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1299) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1299) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1309) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1309) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (1319) adxl345: [11] X:   253 Y:    20 Z:  7824[0m
[0;32mI (1319) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1329) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1329) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1339) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1349) adxl345: [ 0] X:   -11 Y:   251 Z:    20[0m
[0;32mI (1349) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1359) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1359) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1369) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1369) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1379) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1379) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1389) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1389) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1399) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (1399) adxl345: [11] X:   250 Y:    21 Z:  7568[0m
[0;32mI (1409) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1419) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1419) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1429) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1429) adxl345: [ 0] X:    -9 Y:   250 Z:    21[0m
[0;32mI (1439) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1439) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1449) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1449) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1459) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1459) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1469) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1469) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1479) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1489) adxl345: [10] X:     0 Y:  2946 Z:    -9[0m
[0;32mI (1489) adxl345: [11] X:   250 Y:    20 Z:  7824[0m
[0;32mI (1499) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1499) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1509) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1509) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1519) adxl345: [ 0] X:   -10 Y:   249 Z:    24[0m
[0;32mI (1519) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1529) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1529) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1539) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1539) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1549) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1559) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1559) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1569) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1569) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (1579) adxl345: [11] X:   251 Y:    21 Z:  7824[0m
[0;32mI (1579) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1589) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1589) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1599) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1609) adxl345: [ 0] X:   -14 Y:   252 Z:    20[0m
[0;32mI (1609) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1609) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1619) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1629) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1629) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1639) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1639) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1649) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1649) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1659) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (1659) adxl345: [11] X:   251 Y:    26 Z:  7824[0m
[0;32mI (1669) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1669) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1679) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1679) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1689) adxl345: [ 0] X:   -11 Y:   253 Z:    26[0m
[0;32mI (1699) adxl345: [ 1] X:  8336 Y:     0 Z:     0[0m
[0;32mI (1699) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1709) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1709) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1719) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1719) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1729) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1729) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1739) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1739) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (1749) adxl345: [11] X:   251 Y:    23 Z:  7824[0m
[0;32mI (1749) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1759) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1769) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1769) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1779) adxl345: [ 0] X:   -12 Y:   250 Z:    18[0m
[0;32mI (1779) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1789) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1789) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1799) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1799) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1809) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1809) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1819) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1819) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1829) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (1839) adxl345: [11] X:   251 Y:    20 Z:  7568[0m
[0;32mI (1839) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1849) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1849) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1859) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1859) adxl345: [ 0] X:   -11 Y:   249 Z:    21[0m
[0;32mI (1869) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1869) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1879) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1879) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1889) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1889) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1899) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1909) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1909) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1919) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (1919) adxl345: [11] X:   249 Y:    24 Z:  7824[0m
[0;32mI (1929) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (1929) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (1939) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (1939) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (1949) adxl345: [ 0] X:    -9 Y:   251 Z:    22[0m
[0;32mI (1949) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (1959) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (1959) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (1969) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (1979) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (1979) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (1989) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (1989) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (1999) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (1999) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (2009) adxl345: [11] X:   251 Y:    21 Z:  7824[0m
[0;32mI (2009) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2019) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2019) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2029) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2039) adxl345: [ 0] X:   -10 Y:   254 Z:    23[0m
[0;32mI (2039) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2049) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2049) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2059) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2059) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2069) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2069) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2079) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2079) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2089) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (2089) adxl345: [11] X:   251 Y:    22 Z:  7824[0m
[0;32mI (2099) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2099) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2109) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2119) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2119) adxl345: [ 0] X:   -16 Y:   250 Z:    21[0m
[0;32mI (2129) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2129) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2139) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2139) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2149) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2149) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2159) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2159) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2169) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2169) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (2179) adxl345: [11] X:   250 Y:    21 Z:  7568[0m
[0;32mI (2189) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2189) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2199) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2199) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2209) adxl345: [ 0] X:   -13 Y:   251 Z:    24[0m
[0;32mI (2209) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2219) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2219) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2229) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2229) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2239) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2239) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2249) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2259) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2259) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (2269) adxl345: [11] X:   251 Y:    21 Z:  7568[0m
[0;32mI (2269) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2279) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2279) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2289) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2299) adxl345: [ 0] X:   -11 Y:   251 Z:    22[0m
[0;32mI (2299) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2299) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2309) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2309) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2319) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2329) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2329) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2339) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2339) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2349) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (2349) adxl345: [11] X:   253 Y:    24 Z:  7824[0m
[0;32mI (2359) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2359) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2369) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2369) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2379) adxl345: [ 0] X:   -12 Y:   252 Z:    20[0m
[0;32mI (2379) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2389) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2399) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2399) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2409) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2409) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2419) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2419) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2429) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2429) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (2439) adxl345: [11] X:   252 Y:    20 Z:  7824[0m
[0;32mI (2439) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2449) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2449) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2459) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2469) adxl345: [ 0] X:   -12 Y:   252 Z:    25[0m
[0;32mI (2469) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2479) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2479) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2489) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2489) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2499) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2499) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2509) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2509) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2519) adxl345: [10] X:     0 Y:  2946 Z:   -10[0m
[0;32mI (2519) adxl345: [11] X:   251 Y:    21 Z:  7824[0m
[0;32mI (2529) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2529) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2539) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2549) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2549) adxl345: [ 0] X:   -13 Y:   252 Z:    26[0m
[0;32mI (2559) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2559) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2569) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2569) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2579) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2579) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2589) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2589) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2599) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2599) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (2609) adxl345: [11] X:   249 Y:    22 Z:  7568[0m
[0;32mI (2619) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2619) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2629) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2629) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2639) adxl345: [ 0] X:   -15 Y:   251 Z:    18[0m
[0;32mI (2639) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2649) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2649) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2659) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2659) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2669) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2669) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2679) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2689) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2689) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (2699) adxl345: [11] X:   252 Y:    22 Z:  7568[0m
[0;32mI (2699) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2709) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2709) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2719) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2729) adxl345: [ 0] X:   -13 Y:   249 Z:    23[0m
[0;32mI (2729) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2729) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2739) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2739) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2749) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2759) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2759) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2769) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2769) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2779) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (2779) adxl345: [11] X:   251 Y:    25 Z:  7824[0m
[0;32mI (2789) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2789) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2799) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2799) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2809) adxl345: [ 0] X:   -12 Y:   250 Z:    20[0m
[0;32mI (2809) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2819) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2829) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2829) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2839) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2839) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2849) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2849) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2859) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2859) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (2869) adxl345: [11] X:   251 Y:    21 Z:  7824[0m
[0;32mI (2869) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2879) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2879) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2889) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2899) adxl345: [ 0] X:   -14 Y:   251 Z:    23[0m
[0;32mI (2899) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2909) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2909) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2919) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (2919) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (2929) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (2929) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (2939) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (2939) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (2949) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (2949) adxl345: [11] X:   252 Y:    23 Z:  7824[0m
[0;32mI (2959) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (2969) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (2969) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (2979) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (2979) adxl345: [ 0] X:   -13 Y:   251 Z:    21[0m
[0;32mI (2989) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (2989) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (2999) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (2999) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3009) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3009) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3019) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3019) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3029) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3039) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (3039) adxl345: [11] X:   252 Y:    22 Z:  7568[0m
[0;32mI (3049) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3049) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3059) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3059) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3069) adxl345: [ 0] X:   -12 Y:   252 Z:    25[0m
[0;32mI (3069) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3079) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3079) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3089) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3089) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3099) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3109) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3109) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3119) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3119) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (3129) adxl345: [11] X:   252 Y:    20 Z:  7568[0m
[0;32mI (3129) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3139) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3139) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3149) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3159) adxl345: [ 0] X:   -14 Y:   250 Z:    20[0m
[0;32mI (3159) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3159) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3169) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3179) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3179) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3189) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3189) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3199) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3199) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3209) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (3209) adxl345: [11] X:   252 Y:    22 Z:  7824[0m
[0;32mI (3219) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3219) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3229) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3229) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3239) adxl345: [ 0] X:   -12 Y:   250 Z:    22[0m
[0;32mI (3249) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3249) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3259) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3259) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3269) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3269) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3279) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3279) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3289) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3289) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (3299) adxl345: [11] X:   249 Y:    21 Z:  7824[0m
[0;32mI (3299) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3309) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3319) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3319) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3329) adxl345: [ 0] X:   -14 Y:   249 Z:    16[0m
[0;32mI (3329) adxl345: [ 1] X:  8336 Y:     0 Z:     0[0m
[0;32mI (3339) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3339) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3349) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3349) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3359) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3359) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3369) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3369) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3379) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (3389) adxl345: [11] X:   250 Y:    20 Z:  7824[0m
[0;32mI (3389) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3399) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3399) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3409) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3409) adxl345: [ 0] X:    -8 Y:   248 Z:    23[0m
[0;32mI (3419) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3419) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3429) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3429) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3439) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3439) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3449) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3459) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3459) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3469) adxl345: [10] X:     0 Y:  2946 Z:   -12[0m
[0;32mI (3469) adxl345: [11] X:   248 Y:    26 Z:  7568[0m
[0;32mI (3479) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3479) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3489) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3489) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3499) adxl345: [ 0] X:   -14 Y:   251 Z:    19[0m
[0;32mI (3499) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3509) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3509) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3519) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3529) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3529) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3539) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3539) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3549) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3549) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (3559) adxl345: [11] X:   252 Y:    23 Z:  7824[0m
[0;32mI (3559) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3569) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3569) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3579) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3589) adxl345: [ 0] X:   -13 Y:   253 Z:    21[0m
[0;32mI (3589) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3599) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3599) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3609) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3609) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3619) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3619) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3629) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3629) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3639) adxl345: [10] X:     0 Y:  2946 Z:   -11[0m
[0;32mI (3639) adxl345: [11] X:   250 Y:    23 Z:  7824[0m
[0;32mI (3649) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3649) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3659) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3669) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3669) adxl345: [ 0] X:   -14 Y:   251 Z:    24[0m
[0;32mI (3679) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3679) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3689) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3689) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3699) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3699) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3709) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3709) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3719) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3719) adxl345: [10] X:     0 Y:  2946 Z:   -14[0m
[0;32mI (3729) adxl345: [11] X:   253 Y:    24 Z:  7824[0m
[0;32mI (3729) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3739) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3749) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3749) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3759) adxl345: [ 0] X:   -12 Y:   251 Z:    22[0m
[0;32mI (3759) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3769) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3769) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3779) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3779) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3789) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3789) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3799) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3799) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3809) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (3819) adxl345: [11] X:   253 Y:    21 Z:  7568[0m
[0;32mI (3819) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3829) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3829) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3839) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3849) adxl345: [ 0] X:   -14 Y:   252 Z:    24[0m
[0;32mI (3849) adxl345: [ 1] X:  8080 Y:     0 Z:     0[0m
[0;32mI (3849) adxl345: [ 2] X:     0 Y:   229 Z:     0[0m
[0;32mI (3859) adxl345: [ 3] X:     0 Y:     0 Z:     0[0m
[0;32mI (3859) adxl345: [ 4] X:     0 Y:     0 Z: 18944[0m
[0;32mI (3869) adxl345: [ 5] X:   128 Y:    48 Z:  -256[0m
[0;32mI (3869) adxl345: [ 6] X:     7 Y:     0 Z: 15104[0m
[0;32mI (3879) adxl345: [ 7] X:     0 Y:     0 Z:     0[0m
[0;32mI (3889) adxl345: [ 8] X:     0 Y:     0 Z:     0[0m
[0;32mI (3889) adxl345: [ 9] X:     0 Y:     0 Z:  2060[0m
[0;32mI (3899) adxl345: [10] X:     0 Y:  2946 Z:   -13[0m
[0;32mI (3899) adxl345: [11] X:   251 Y:    20 Z:  7568[0m
[0;32mI (3909) adxl345: [12] X:     0 Y:     0 Z:     0[0m
[0;32mI (3909) adxl345: [13] X:   229 Y:     0 Z:     0[0m
[0;32mI (3919) adxl345: [14] X:     0 Y:     0 Z:     0[0m
[0;32mI (3919) adxl345: [15] X:     0 Y: 18944 Z:   128[0m
[0;32mI (3929) adxl345: [ 0] X:   -11 Y:   247 Z:    23[0m
