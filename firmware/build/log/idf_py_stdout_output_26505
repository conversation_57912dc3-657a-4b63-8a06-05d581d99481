[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264)ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x4004883b
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (188) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (191) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      3b2c6ea[0m
[0;32mI (201) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (207) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (217) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (222) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (227) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (239) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (251) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (187) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (187) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
[0;32mI (147) esp_image: segment 4: paddr=0003a0b0 vaddr=40375eb0 size=0d930h ( 55600) load[0m
[0;32mI (166) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (166) boot: Disabling RNG early entropy source...[0m
[0;32mI (178) cpu_start: Multicore app[0m
[0;32mI (187) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (190) app_init: Project name:     firmware[0m
[0;32mI (195) app_init: App version:      3b2c6ea[0m
[0;32mI (200) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (206) app_init: ELF file SHA256:  2a18f632c...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (221) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (226) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (238) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (244) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (256) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (319) i2c: i2c_set_pin(970): scl gpio number error[0m
ESP_ERROR_CHECK failed: esp_err_t 0x102 (ESP_ERR_INVALID_ARG) at 0x42008cc0
file: "./main/firmware.c" line 105
func: app_main
expression: i2c_master_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc99450 0x4037acdd:0x3fc99470 0x40381699:0x3fc99490 0x4037acd3:0x3fc99500 0x42008cc0:0x3fc99530 0x42019763:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 2a18f632c

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (26) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (29) boot: chip revision: v0.2[0m
[0;32mI (33) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (47) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (57) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (68) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (83) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b6c4h ( 46788) map[0m
[0;32mI (111) esp_image: segment 1: paddr=0001b6ec vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (114) esp_image: segment 2: paddr=0001e168 vaddr=40374000 size=01eb0h (  7856) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a088h (106632) map[0m
