[0/1] Re-running CMake...
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file /Users/<USER>/Workspace/master/firmware/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- App "firmware" version: f08ec8f-dirty
-- Adding linker script /Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace /Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock /Users/<USER>/.espressif/esp-idf/v5.3.2/components/console /Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx /Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif_stack /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py /Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal /Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap /Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser /Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test /Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154 /Users/<USER>/.espressif/esp-idf/v5.3.2/components/json /Users/<USER>/.espressif/esp-idf/v5.3.2/components/log /Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip /Users/<USER>/Workspace/master/firmware/main /Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls /Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib /Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash /Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider /Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table /Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon /Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread /Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash /Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport /Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element /Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp /Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity /Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb /Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant /Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa
-- Configuring done (2.5s)
-- Generating done (0.4s)
-- Build files have been written to: /Users/<USER>/Workspace/master/firmware/build
[1/12] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/Workspace/master/firmware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/Workspace/master/firmware/build/bootloader/bootloader.bin
Bootloader binary size 0x5490 bytes. 0x2b70 bytes (34%) free.
[2/12] No install step for 'bootloader'
[3/12] Completed 'bootloader'
[4/12] Building C object esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj
[5/12] Linking C static library esp-idf/esp_app_format/libesp_app_format.a
[6/12] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/firmware.c.obj
[7/12] Linking C static library esp-idf/main/libmain.a
[8/12] Generating ld/sections.ld
[9/12] Linking CXX executable firmware.elf
[10/12] Generating binary image from built executable
esptool.py v4.8.1
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /Users/<USER>/Workspace/master/firmware/build/firmware.bin
[11/12] cd /Users/<USER>/Workspace/master/firmware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/Workspace/master/firmware/build/partition_table/partition-table.bin /Users/<USER>/Workspace/master/firmware/build/firmware.bin
firmware.bin binary size 0x383b0 bytes. Smallest app partition is 0x100000 bytes. 0xc7c50 bytes (78%) free.
