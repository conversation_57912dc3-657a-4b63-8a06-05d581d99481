2mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI �ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bd44h ( 48452) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bd6c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e808 vaddr=40374000 size=01810h (  6160) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a570h (107888) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a598 vaddr=40375810 size=0dfd0h ( 57296) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (197) app_init: App version:      f08ec8f-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:21:18[0m
[0;32mI (208) app_init: ELF file SHA256:  7109948d0...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (273) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (441) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (441) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (451) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (451) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (461) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (461) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (461) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (471) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (471) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (481) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (491) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (491) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0;32mI (501) DUAL_ADXL345: Sensor 1 data (timestamp: 19, samples: 16):[0m
[0;32mI (501) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (511) DUAL_ADXL345:   [0] X:   -13 Y:   -27 Z:   264[0m
[0;32mI (521) DUAL_ADXL345:   [1] X:   -15 Y:   -24 Z:   263[0m
[0;32mI (531) DUAL_ADXL345:   [2] X:   -13 Y:   -25 Z:   264[0m
[0;32mI (531) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (541) DUAL_ADXL345: Sensor 1 data (timestamp: 19, samples: 16):[0m
[0;32mI (551) DUAL_ADXL345:   [0] X:   -16 Y:   -29 Z:   259[0m
[0;32mI (551) DUAL_ADXL345:   [1] X:   -12 Y:   -30 Z:   261[0m
[0;32mI (561) DUAL_ADXL345:   [2] X:   -17 Y:   -28 Z:   261[0m
[0;32mI (561) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (571) DUAL_ADXL345: Sensor 1 data (timestamp: 23, samples: 16):[0m
[0;32mI (581) DUAL_ADXL345:   [0] X:   -13 Y:   -28 Z:   262[0m
[0;32mI (581) DUAL_ADXL345:   [1] X:   -15 Y:   -25 Z:   259[0m
[0;32mI (591) DUAL_ADXL345:   [2] X:   -11 Y:   -26 Z:   263[0m
[0;32mI (591) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (601) DUAL_ADXL345: Sensor 2 data (timestamp: 24, samples: 16):[0m
[0;32mI (611) DUAL_ADXL345:   [0] X:   120 Y:   196 Z:   -15[0m
[0;32mI (611) DUAL_ADXL345:   [1] X:   130 Y:   213 Z:   -14[0m
[0;32mI (621) DUAL_ADXL345:   [2] X:   129 Y:   214 Z:   -16[0m
[0;32mI (621) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (631) DUAL_ADXL345: Sensor 1 data (timestamp: 27, samples: 16):[0m
[0;32mI (641) DUAL_ADXL345:   [0] X:   -14 Y:   -29 Z:   259[0m
[0;32mI (641) DUAL_ADXL345:   [1] X:   -19 Y:   -27 Z:   269[0m
[0;32mI (651) DUAL_ADXL345:   [2] X:   -13 Y:   -25 Z:   273[0m
[0;32mI (651) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (661) DUAL_ADXL345: Sensor 2 data (timestamp: 28, samples: 16):[0m
[0;32mI (661) DUAL_ADXL345:   [0] X:   132 Y:   221 Z:   -16[0m
[0;32mI (671) DUAL_ADXL345:   [1] X:   131 Y:   215 Z:   -11[0m
[0;32mI (681) DUAL_ADXL345:   [2] X:   129 Y:   217 Z:   -15[0m
[0;32mI (681) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (691) DUAL_ADXL345: Sensor 1 data (timestamp: 31, samples: 16):[0m
[0;32mI (691) DUAL_ADXL345:   [0] X:   -13 Y:   -29 Z:   262[0m
[0;32mI (701) DUAL_ADXL345:   [1] X:   -15 Y:   -27 Z:   255[0m
[0;32mI (711) DUAL_ADXL345:   [2] X:   -10 Y:   -27 Z:   258[0m
[0;32mI (711) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (721) DUAL_ADXL345: Sensor 2 data (timestamp: 32, samples: 16):[0m
[0;32mI (721) DUAL_ADXL345:   [0] X:   134 Y:   215 Z:   -13[0m
[0;32mI (731) DUAL_ADXL345:   [1] X:   134 Y:   211 Z:   -16[0m
[0;32mI (731) DUAL_ADXL345:   [2] X:   126 Y:   215 Z:   -17[0m
[0;32mI (741) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (751) DUAL_ADXL345: Sensor 1 data (timestamp: 35, samples: 16):[0m
[0;32mI (751) DUAL_ADXL345:   [0] X:   -11 Y:   -31 Z:   263[0m
[0;32mI (761) DUAL_ADXL345:   [1] X:   -15 Y:   -23 Z:   260[0m
[0;32mI (761) DUAL_ADXL345:   [2] X:   -11 Y:   -29 Z:   260[0m
[0;32mI (771) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (771) DUAL_ADXL345: Sensor 2 data (timestamp: 36, samples: 16):[0m
[0;32mI (781) DUAL_ADXL345:   [0] X:   131 Y:   223 Z:   -19[0m
[0;32mI (791) DUAL_ADXL345:   [1] X:   134 Y:   218 Z:   -21[0m
[0;32mI (791) DUAL_ADXL345:   [2] X:   135 Y:   215 Z:   -23[0m
[0;32mI (801) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (801) DUAL_ADXL345: Sensor 1 data (timestamp: 39, samples: 16):[0m
[0;32mI (811) DUAL_ADXL345:   [0] X:   -17 Y:   -22 Z:   262[0m
[0;32mI (821) DUAL_ADXL345:   [1] X:   -12 Y:   -25 Z:   258[0m
[0;32mI (821) DUAL_ADXL345:   [2] X:   -17 Y:   -22 Z:   259[0m
[0;32mI (831) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (831) DUAL_ADXL345: Sensor 2 data (timestamp: 40, samples: 16):[0m
[0;32mI (841) DUAL_ADXL345:   [0] X:   132 Y:   214 Z:   -21[0m
[0;32mI (851) DUAL_ADXL345:   [1] X:   135 Y:   214 Z:   -16[0m
[0;32mI (851) DUAL_ADXL345:   [2] X:   129 Y:   207 Z:   -15[0m
[0;32mI (861) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (861) DUAL_ADXL345: Sensor 1 data (timestamp: 43, samples: 16):[0m
[0;32mI (871) DUAL_ADXL345:   [0] X:   -10 Y:   -29 Z:   263[0m
[0;32mI (871) DUAL_ADXL345:   [1] X:   -16 Y:   -21 Z:   255[0m
[0;32mI (881) DUAL_ADXL345:   [2] X:    -9 Y:   -25 Z:   262[0m
[0;32mI (891) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (891) DUAL_ADXL345: Sensor 2 data (timestamp: 44, samples: 16):[0m
[0;32mI (901) DUAL_ADXL345:   [0] X:   142 Y:   227 Z:    -9[0m
[0;32mI (901) DUAL_ADXL345:   [1] X:   126 Y:   225 Z:   -15[0m
[0;32mI (911) DUAL_ADXL345:   [2] X:   127 Y:   210 Z:   -19[0m
[0;32mI (921) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (921) DUAL_ADXL345: Sensor 1 data (timestamp: 47, samples: 16):[0m
[0;32mI (931) DUAL_ADXL345:   [0] X:   -17 Y:   -27 Z:   263[0m
[0;32mI (931) DUAL_ADXL345:   [1] X:   -18 Y:   -28 Z:   264[0m
[0;32mI (941) DUAL_ADXL345:   [2] X:   -18 Y:   -25 Z:   265[0m
[0;32mI (941) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (951) DUAL_ADXL345: Sensor 2 data (timestamp: 48, samples: 16):[0m
[0;32mI (961) DUAL_ADXL345:   [0] X:   128 Y:   212 Z:   -17[0m
[0;32mI (961) DUAL_ADXL345:   [1] X:   126 Y:   211 Z:   -22[0m
[0;32mI (971) DUAL_ADXL345:   [2] X:   130 Y:   213 Z:   -19[0m
[0;32mI (971) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (981) DUAL_ADXL345: Sensor 1 data (timestamp: 51, samples: 16):[0m
[0;32mI (991) DUAL_ADXL345:   [0] X:   -15 Y:   -26 Z:   261[0m
[0;32mI (991) DUAL_ADXL345:   [1] X:   -16 Y:   -28 Z:   261[0m
[0;32mI (1001) DUAL_ADXL345:   [2] X:   -12 Y:   -28 Z:   265[0m
[0;32mI (1001) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1011) DUAL_ADXL345: Sensor 2 data (timestamp: 52, samples: 16):[0m
[0;32mI (491) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (1021) DUAL_ADXL345:   [0] X:   124 Y:   219 Z:   -13[0m
[0;32mI (1031) DUAL_ADXL345:   [1] X:   134 Y:   212 Z:    -7[0m
[0;32mI (1031) DUAL_ADXL345:   [2] X:   132 Y:   216 Z:   -13[0m
[0;32mI (1041) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1041) DUAL_ADXL345: Sensor 1 data (timestamp: 55, samples: 16):[0m
[0;32mI (1051) DUAL_ADXL345:   [0] X:   -21 Y:   -33 Z:   261[0m
[0;32mI (1061) DUAL_ADXL345:   [1] X:    -6 Y:   -26 Z:   268[0m
[0;32mI (1061) DUAL_ADXL345:   [2] X:   -16 Y:   -28 Z:   264[0m
[0;32mI (1071) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1071) DUAL_ADXL345: Sensor 2 data (timestamp: 56, samples: 16):[0m
[0;32mI (1081) DUAL_ADXL345:   [0] X:   129 Y:   219 Z:   -10[0m
[0;32mI (1031) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (1091) DUAL_ADXL345:   [1] X:   137 Y:   222 Z:   -14[0m
[0;32mI (1101) DUAL_ADXL345:   [2] X:   135 Y:   216 Z:   -13[0m
[0;32mI (1111) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1111) DUAL_ADXL345: Sensor 1 data (timestamp: 59, samples: 16):[0m
[0;32mI (1121) DUAL_ADXL345:   [0] X:   -15 Y:   -27 Z:   261[0m
[0;32mI (1121) DUAL_ADXL345:   [1] X:   -12 Y:   -24 Z:   262[0m
[0;32mI (1131) DUAL_ADXL345:   [2] X:   -17 Y:   -17 Z:   271[0m
[0;32mI (1141) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1141) DUAL_ADXL345: Sensor 2 data (timestamp: 60, samples: 16):[0m
[0;32mI (1151) DUAL_ADXL345:   [0] X:   128 Y:   209 Z:   -22[0m
[0;32mI (1151) DUAL_ADXL345:   [1] X:   132 Y:   220 Z:   -15[0m
[0;32mI (1161) DUAL_ADXL345:   [2] X:   130 Y:   220 Z:   -21[0m
[0;32mI (1161) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1171) DUAL_ADXL345: Sensor 1 data (timestamp: 63, samples: 16):[0m
[0;32mI (1181) DUAL_ADXL345:   [0] X:   -15 Y:   -29 Z:   259[0m
[0;32mI (1181) DUAL_ADXL345:   [1] X:   -18 Y:   -22 Z:   262[0m
[0;32mI (1191) DUAL_ADXL345:   [2] X:   -20 Y:   -19 Z:   268[0m
[0;32mI (1191) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1201) DUAL_ADXL345: Sensor 2 data (timestamp: 64, samples: 16):[0m
[0;32mI (1211) DUAL_ADXL345:   [0] X:   128 Y:   209 Z:   -22[0m
[0;32mI (1211) DUAL_ADXL345:   [1] X:   129 Y:   218 Z:    -9[0m
[0;32mI (1221) DUAL_ADXL345:   [2] X:   129 Y:   215 Z:   -21[0m
[0;32mI (1221) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1231) DUAL_ADXL345: Sensor 1 data (timestamp: 67, samples: 16):[0m
[0;32mI (1241) DUAL_ADXL345:   [0] X:    -9 Y:   -29 Z:   264[0m
[0;32mI (1241) DUAL_ADXL345:   [1] X:   -10 Y:   -24 Z:   268[0m
[0;32mI (1251) DUAL_ADXL345:   [2] X:    -8 Y:   -25 Z:   267[0m
[0;32mI (1251) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1261) DUAL_ADXL345: Sensor 2 data (timestamp: 68, samples: 16):[0m
[0;32mI (1271) DUAL_ADXL345:   [0] X:   128 Y:   214 Z:   -12[0m
[0;32mI (1271) DUAL_ADXL345:   [1] X:   124 Y:   212 Z:   -19[0m
[0;32mI (1281) DUAL_ADXL345:   [2] X:   126 Y:   213 Z:   -15[0m
[0;32mI (1281) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1291) DUAL_ADXL345: Sensor 1 data (timestamp: 71, samples: 16):[0m
[0;32mI (1301) DUAL_ADXL345:   [0] X:   -12 Y:   -26 Z:   259[0m
[0;32mI (1301) DUAL_ADXL345:   [1] X:   -16 Y:   -22 Z:   257[0m
[0;32mI (1311) DUAL_ADXL345:   [2] X:   -13 Y:   -27 Z:   257[0m
[0;32mI (1311) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1321) DUAL_ADXL345: Sensor 2 data (timestamp: 72, samples: 16):[0m
[0;32mI (1331) DUAL_ADXL345:   [0] X:   131 Y:   218 Z:   -16[0m
[0;32mI (1331) DUAL_ADXL345:   [1] X:   133 Y:   217 Z:   -11[0m
[0;32mI (1341) DUAL_ADXL345:   [2] X:   130 Y:   216 Z:   -12[0m
[0;32mI (1341) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1351) DUAL_ADXL345: Sensor 1 data (timestamp: 75, samples: 16):[0m
[0;32mI (1351) DUAL_ADXL345:   [0] X:   -10 Y:   -27 Z:   259[0m
[0;32mI (1361) DUAL_ADXL345:   [1] X:   -16 Y:   -21 Z:   257[0m
[0;32mI (1371) DUAL_ADXL345:   [2] X:   -18 Y:   -27 Z:   258[0m
[0;32mI (1371) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1381) DUAL_ADXL345: Sensor 2 data (timestamp: 76, samples: 16):[0m
[0;32mI (1381) DUAL_ADXL345:   [0] X:   124 Y:   209 Z:   -16[0m
[0;32mI (1391) DUAL_ADXL345:   [1] X:   127 Y:   213 Z:   -18[0m
[0;32mI (1401) DUAL_ADXL345:   [2] X:   131 Y:   214 Z:   -20[0m
[0;32mI (1401) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1411) DUAL_ADXL345: Sensor 1 data (timestamp: 79, samples: 16):[0m
[0;32mI (1411) DUAL_ADXL345:   [0] X:   -18 Y:   -25 Z:   263[0m
[0;32mI (1421) DUAL_ADXL345:   [1] X:   -16 Y:   -30 Z:   265[0m
[0;32mI (1431) DUAL_ADXL345:   [2] X:    -6 Y:   -25 Z:   265[0m
[0;32mI (1431) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1441) DUAL_ADXL345: Sensor 2 data (timestamp: 80, samples: 16):[0m
[0;32mI (1441) DUAL_ADXL345:   [0] X:   139 Y:   216 Z:   -17[0m
[0;32mI (1451) DUAL_ADXL345:   [1] X:   134 Y:   218 Z:    -4[0m
[0;32mI (1461) DUAL_ADXL345:   [2] X:   134 Y:   218 Z:    -7[0m
[0;32mI (1461) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1471) DUAL_ADXL345: Sensor 1 data (timestamp: 83, samples: 16):[0m
[0;32mI (1471) DUAL_ADXL345:   [0] X:   -15 Y:   -26 Z:   258[0m
[0;32mI (1481) DUAL_ADXL345:   [1] X:   -15 Y:   -24 Z:   261[0m
[0;32mI (1491) DUAL_ADXL345:   [2] X:   -12 Y:   -24 Z:   263[0m
[0;32mI (1491) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1501) DUAL_ADXL345: Sensor 2 data (timestamp: 84, samples: 16):[0m
[0;32mI (1501) DUAL_ADXL345:   [0] X:   128 Y:   209 Z:   -19[0m
[0;32mI (1511) DUAL_ADXL345:   [1] X:   130 Y:   216 Z:    -5[0m
[0;32mI (1521) DUAL_ADXL345:   [2] X:   132 Y:   208 Z:   -13[0m
[0;32mI (1521) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1531) DUAL_ADXL345: Sensor 1 data (timestamp: 87, samples: 16):[0m
[0;32mI (1531) DUAL_ADXL345:   [0] X:   -12 Y:   -31 Z:   261[0m
[0;32mI (1541) DUAL_ADXL345:   [1] X:   -18 Y:   -30 Z:   267[0m
[0;32mI (1541) DUAL_ADXL345:   [2] X:   -14 Y:   -26 Z:   263[0m
[0;32mI (1551) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1561) DUAL_ADXL345: Sensor 2 data (timestamp: 88, samples: 16):[0m
[0;32mI (1561) DUAL_ADXL345:   [0] X:   128 Y:   215 Z:   -16[0m
[0;32mI (1571) DUAL_ADXL345:   [1] X:   124 Y:   217 Z:   -18[0m
[0;32mI (1571) DUAL_ADXL345:   [2] X:   127 Y:   213 Z:   -20[0m
[0;32mI (1581) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1591) DUAL_ADXL345: Sensor 1 data (timestamp: 91, samples: 16):[0m
[0;32mI (1591) DUAL_ADXL345:   [0] X:   -13 Y:   -27 Z:   267[0m
[0;32mI (1601) DUAL_ADXL345:   [1] X:   -12 Y:   -25 Z:   262[0m
[0;32mI (1601) DUAL_ADXL345:   [2] X:   -17 Y:   -20 Z:   266[0m
[0;32mI (1611) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1621) DUAL_ADXL345: Sensor 2 data (timestamp: 92, samples: 16):[0m
[0;32mI (1621) DUAL_ADXL345:   [0] X:   126 Y:   216 Z:   -10[0m
[0;32mI (1631) DUAL_ADXL345:   [1] X:   129 Y:   213 Z:   -16[0m
[0;32mI (1631) DUAL_ADXL345:   [2] X:   127 Y:   215 Z:   -16[0m
[0;32mI (1641) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1641) DUAL_ADXL345: Sensor 1 data (timestamp: 95, samples: 16):[0m
[0;32mI (1651) DUAL_ADXL345:   [0] X:   -20 Y:   -26 Z:   255[0m
[0;32mI (1661) DUAL_ADXL345:   [1] X:   -16 Y:   -25 Z:   259[0m
[0;32mI (1661) DUAL_ADXL345:   [2] X:   -18 Y:   -23 Z:   260[0m
[0;32mI (1671) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1671) DUAL_ADXL345: Sensor 2 data (timestamp: 96, samples: 16):[0m
[0;32mI (1681) DUAL_ADXL345:   [0] X:   137 Y:   214 Z:   -10[0m
[0;32mI (1691) DUAL_ADXL345:   [1] X:   133 Y:   220 Z:    -8[0m
[0;32mI (1691) DUAL_ADXL345:   [2] X:   134 Y:   223 Z:    -9[0m
[0;32mI (1701) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1701) DUAL_ADXL345: Sensor 1 data (timestamp: 100, samples: 16):[0m
[0;32mI (1711) DUAL_ADXL345:   [0] X:   -14 Y:   -43 Z:   268[0m
[0;32mI (1101) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;32mI (1721) DUAL_ADXL345:   [1] X:    -9 Y:   -37 Z:   271[0m
[0;32mI (1731) DUAL_ADXL345:   [2] X:   -10 Y:   -31 Z:   269[0m
[0;32mI (1741) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1741) DUAL_ADXL345: Sensor 2 data (timestamp: 100, samples: 16):[0m
[0;32mI (1751) DUAL_ADXL345:   [0] X:   134 Y:   210 Z:   -33[0m
[0;32mI (1751) DUAL_ADXL345:   [1] X:   134 Y:   206 Z:   -31[0m
[0;32mI (1761) DUAL_ADXL345:   [2] X:   133 Y:   204 Z:   -35[0m
[0;32mI (1771) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1771) DUAL_ADXL345: Sensor 1 data (timestamp: 104, samples: 16):[0m
[0;32mI (1781) DUAL_ADXL345:   [0] X:   -13 Y:   -18 Z:   259[0m
[0;32mI (1781) DUAL_ADXL345:   [1] X:    -8 Y:   -13 Z:   254[0m
[0;32mI (1791) DUAL_ADXL345:   [2] X:   -16 Y:   -13 Z:   257[0m
[0;32mI (1801) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1801) DUAL_ADXL345: Sensor 2 data (timestamp: 104, samples: 16):[0m
[0;32mI (1811) DUAL_ADXL345:   [0] X:   116 Y:   218 Z:     1[0m
[0;32mI (1811) DUAL_ADXL345:   [1] X:   113 Y:   221 Z:    -1[0m
[0;32mI (1821) DUAL_ADXL345:   [2] X:   114 Y:   229 Z:     1[0m
[0;32mI (1831) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1831) DUAL_ADXL345: Sensor 1 data (timestamp: 108, samples: 16):[0m
[0;32mI (1841) DUAL_ADXL345:   [0] X:   -15 Y:   -24 Z:   259[0m
[0;32mI (1841) DUAL_ADXL345:   [1] X:   -15 Y:   -25 Z:   261[0m
[0;32mI (1851) DUAL_ADXL345:   [2] X:   -16 Y:   -29 Z:   256[0m
[0;32mI (1861) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1861) DUAL_ADXL345: Sensor 2 data (timestamp: 108, samples: 16):[0m
[0;32mI (1871) DUAL_ADXL345:   [0] X:   134 Y:   205 Z:   -17[0m
[0;32mI (1871) DUAL_ADXL345:   [1] X:   136 Y:   210 Z:   -14[0m
[0;32mI (1881) DUAL_ADXL345:   [2] X:   144 Y:   210 Z:   -20[0m
[0;32mI (1891) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1891) DUAL_ADXL345: Sensor 1 data (timestamp: 112, samples: 16):[0m
[0;32mI (1901) DUAL_ADXL345:   [0] X:   -17 Y:   -31 Z:   271[0m
[0;32mI (1901) DUAL_ADXL345:   [1] X:   -13 Y:   -23 Z:   271[0m
[0;32mI (1911) DUAL_ADXL345:   [2] X:    -8 Y:   -28 Z:   266[0m
[0;32mI (1911) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1921) DUAL_ADXL345: Sensor 2 data (timestamp: 112, samples: 16):[0m
[0;32mI (1931) DUAL_ADXL345:   [0] X:   128 Y:   209 Z:   -10[0m
[0;32mI (1931) DUAL_ADXL345:   [1] X:   134 Y:   211 Z:   -21[0m
[0;32mI (1941) DUAL_ADXL345:   [2] X:   130 Y:   203 Z:   -17[0m
[0;32mI (1941) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1951) DUAL_ADXL345: Sensor 1 data (timestamp: 116, samples: 16):[0m
[0;32mI (1961) DUAL_ADXL345:   [0] X:   -18 Y:   -25 Z:   255[0m
[0;32mI (1961) DUAL_ADXL345:   [1] X:   -18 Y:   -29 Z:   271[0m
[0;32mI (1971) DUAL_ADXL345:   [2] X:   -12 Y:   -24 Z:   265[0m
[0;32mI (1971) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1981) DUAL_ADXL345: Sensor 2 data (timestamp: 116, samples: 16):[0m
[0;32mI (1991) DUAL_ADXL345:   [0] X:   121 Y:   221 Z:   -13[0m
[0;32mI (1991) DUAL_ADXL345:   [1] X:   123 Y:   227 Z:    -9[0m
[0;32mI (2001) DUAL_ADXL345:   [2] X:   130 Y:   224 Z:   -18[0m
[0;32mI (2011) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2011) DUAL_ADXL345: Sensor 1 data (timestamp: 120, samples: 16):[0m
[0;32mI (2021) DUAL_ADXL345:   [0] X:   -11 Y:   -37 Z:   267[0m
[0;32mI (2021) DUAL_ADXL345:   [1] X:    -9 Y:   -34 Z:   266[0m
[0;32mI (2031) DUAL_ADXL345:   [2] X:   -11 Y:   -33 Z:   263[0m
[0;32mI (2031) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2041) DUAL_ADXL345: Sensor 2 data (timestamp: 120, samples: 16):[0m
[0;32mI (2051) DUAL_ADXL345:   [0] X:   139 Y:   211 Z:   -19[0m
[0;32mI (2051) DUAL_ADXL345:   [1] X:   137 Y:   217 Z:   -21[0m
[0;32mI (2061) DUAL_ADXL345:   [2] X:   132 Y:   217 Z:   -25[0m
[0;32mI (2061) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2071) DUAL_ADXL345: Sensor 1 data (timestamp: 124, samples: 16):[0m
[0;32mI (2081) DUAL_ADXL345:   [0] X:   -16 Y:   -22 Z:   261[0m
[0;32mI (2081) DUAL_ADXL345:   [1] X:   -16 Y:   -16 Z:   261[0m
[0;32mI (2091) DUAL_ADXL345:   [2] X:   -13 Y:   -20 Z:   258[0m
[0;32mI (2091) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2101) DUAL_ADXL345: Sensor 2 data (timestamp: 124, samples: 16):[0m
[0;32mI (2111) DUAL_ADXL345:   [0] X:   123 Y:   211 Z:   -14[0m
[0;32mI (2111) DUAL_ADXL345:   [1] X:   127 Y:   206 Z:   -11[0m
[0;32mI (2121) DUAL_ADXL345:   [2] X:   128 Y:   205 Z:   -17[0m
