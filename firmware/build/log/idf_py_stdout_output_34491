crΌ`jb�j5
[0;32mI (739) ADXL345: [15] X:    -7 Y:    -7 Z:   263[ 4] X:    -8 Y:    -7 Z:   265[0m
[0;32mI (769) ADXL345: [ 5] X:    -7 Y:    -4 Z:   263[0m
[0;32mI (779) ADXL345: [ 6] X:    -7 Y:    -7 Z:   263[0m
[0;32mI (789) ADXL345: [ ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b704h ( 46852) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001b72c vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e1a8 vaddr=40374000 size=01e70h (  7792) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a09ch (106652) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a0c4 vaddr=40375e70 size=0d970h ( 55664) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (188) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (191) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      3b2c6ea[0m
[0;32mI (201) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (207) app_init: ELF file SHA256:  b4e3d6d0d...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (217) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (222) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (227) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (239) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (251) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;32mI (319) ADXL345: ADXL345 ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (329) ADXL345: [ 0] X:    -8 Y:    -6 Z:   265[0m
[0;32mI (329) ADXL345: [ 1] X:    -9 Y:    -3 Z:   262[0m
[0;32mI (339) ADXL345: [ 2] X:    -9 Y:    -4 Z:   265[0m
[0;32mI (339) ADXL345: [ 3] X:    -8 Y:    -7 Z:   265[0m
[0;32mI (349) ADXL345: [ 4] X:    -7 Y:    -7 Z:   264[0m
[0;32mI (349) ADXL345: [ 5] X:    -6 Y:    -6 Z:   264[0m
[0;32mI (359) ADXL345: [ 6] X:    -8 Y:    -4 Z:   265[0m
[0;32mI (359) ADXL345: [ 7] X:    -7 Y:    -8 Z:   264[0m
[0;32mI (369) ADXL345: [ 8] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (369) ADXL345: [ 9] X:    -8 Y:    -7 Z:   262[0m
[0;32mI (379) ADXL345: [10] X:    -6 Y:    -6 Z:   263[0m
[0;32mI (379) ADXL345: [11] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (389) ADXL345: [12] X:   -10 Y:    -4 Z:   262[0m
[0;32mI (389) ADXL345: [13] X:    -9 Y:    -3 Z:   262[0m
[0;32mI (399) ADXL345: [14] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (399) ADXL345: [15] X:    -6 Y:    -5 Z:   260[0m
[0;32mI (409) ADXL345: [ 0] X:    -8 Y:    -6 Z:   260[0m
[0;32mI (409) ADXL345: [ 1] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (419) ADXL345: [ 2] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (429) ADXL345: [ 3] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (429) ADXL345: [ 4] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (439) ADXL345: [ 5] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (439) ADXL345: [ 6] X:    -7 Y:    -5 Z:   265[0m
[0;32mI (449) ADXL345: [ 7] X:    -6 Y:    -6 Z:   262[0m
[0;32mI (449) ADXL345: [ 8] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (459) ADXL345: [ 9] X:    -7 Y:    -7 Z:   261[0m
[0;32mI (459) ADXL345: [10] X:    -5 Y:    -6 Z:   262[0m
[0;32mI (469) ADXL345: [11] X:   -10 Y:    -3 Z:   259[0m
[0;32mI (469) ADXL345: [12] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (479) ADXL345: [13] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (479) ADXL345: [14] X:    -9 Y:    -4 Z:   261[0m
[0;32mI (489) ADXL345: [15] X:    -3 Y:    -6 Z:   262[0m
[0;32mI (489) ADXL345: [ 0] X:   -11 Y:    -1 Z:   263[0m
[0;32mI (499) ADXL345: [ 1] X:    -8 Y:    -1 Z:   262[0m
[0;32mI (499) ADXL345: [ 2] X:    -6 Y:    -3 Z:   263[0m
[0;32mI (509) ADXL345: [ 3] X:    -5 Y:    -3 Z:   264[0m
[0;32mI (519) ADXL345: [ 4] X:    -5 Y:    -8 Z:   264[0m
[0;32mI (519) ADXL345: [ 5] X:    -6 Y:    -5 Z:   264[0m
[0;32mI (529) ADXL345: [ 6] X:    -8 Y:    -5 Z:   266[0m
[0;32mI (529) ADXL345: [ 7] X:   -10 Y:    -7 Z:   265[0m
[0;32mI (539) ADXL345: [ 8] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (539) ADXL345: [ 9] X:   -10 Y:    -7 Z:   265[0m
[0;32mI (549) ADXL345: [10] X:    -7 Y:    -4 Z:   265[0m
[0;32mI (549) ADXL345: [11] X:    -9 Y:    -5 Z:   263[0m
[0;32mI (559) ADXL345: [12] X:    -7 Y:    -5 Z:   262[0m
[0;32mI (559) ADXL345: [13] X:   -10 Y:    -4 Z:   265[0m
[0;32mI (569) ADXL345: [14] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (569) ADXL345: [15] X:    -5 Y:    -9 Z:   263[0m
[0;32mI (579) ADXL345: [ 0] X:    -9 Y:    -6 Z:   262[0m
[0;32mI (579) ADXL345: [ 1] X:    -8 Y:    -2 Z:   262[0m
[0;32mI (589) ADXL345: [ 2] X:    -5 Y:    -5 Z:   261[0m
[0;32mI (589) ADXL345: [ 3] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (599) ADXL345: [ 4] X:    -8 Y:    -4 Z:   262[0m
[0;32mI (609) ADXL345: [ 5] X:    -5 Y:    -6 Z:   263[0m
[0;32mI (609) ADXL345: [ 6] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (619) ADXL345: [ 7] X:    -9 Y:    -6 Z:   265[0m
[0;32mI (619) ADXL345: [ 8] X:   -10 Y:    -5 Z:   266[0m
[0;32mI (629) ADXL345: [ 9] X:    -9 Y:    -3 Z:   264[0m
[0;32mI (629) ADXL345: [10] X:    -7 Y:    -5 Z:   262[0m
[0;32mI (639) ADXL345: [11] X:    -6 Y:    -7 Z:   265[0m
[0;32mI (639) ADXL345: [12] X:    -9 Y:    -6 Z:   265[0m
[0;32mI (649) ADXL345: [13] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (649) ADXL345: [14] X:    -8 Y:    -8 Z:   264[0m
[0;32mI (659) ADXL345: [15] X:    -7 Y:    -6 Z:   268[0m
[0;32mI (659) ADXL345: [ 0] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (669) ADXL345: [ 1] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (669) ADXL345: [ 2] X:    -7 Y:    -8 Z:   262[0m
[0;32mI (679) ADXL345: [ 3] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (679) ADXL345: [ 4] X:    -9 Y:    -3 Z:   265[0m
[0;32mI (689) ADXL345: [ 5] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (699) ADXL345: [ 6] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (699) ADXL345: [ 7] X:    -7 Y:    -4 Z:   265[0m
[0;32mI (709) ADXL345: [ 8] X:    -8 Y:    -7 Z:   264[0m
[0;32mI (709) ADXL345: [ 9] X:    -6 Y:    -7 Z:   265[0m
[0;32mI (719) ADXL345: [10] X:    -7 Y:    -7 Z:   263[0m
[0;32mI (719) ADXL345: [11] X:    -6 Y:    -6 Z:   262[0m
[0;32mI (729) ADXL345: [12] X:    -8 Y:    -7 Z:   264[0m
[0;32mI (729) ADXL345: [13] X:    -5 Y:    -7 Z:   265[0m
[0;32mI (739) ADXL345: [14] X:    -9 Y:    -8 Z:   264[0m
[0;32mI (739) ADXL345: [15] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (749) ADXL345: [ 0] X:    -9 Y:    -7 Z:   266[0m
[0;32mI (749) ADXL345: [ 1] X:    -6 Y:    -5 Z:   265[0m
[0;32mI (759) ADXL345: [ 2] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (759) ADXL345: [ 3] X:    -9 Y:    -4 Z:   262[0m
[0;32mI (769) ADXL345: [ 4] X:    -7 Y:    -3 Z:   264[0m
[0;32mI (769) ADXL345: [ 5] X:   -10 Y:    -2 Z:   261[0m
[0;32mI (779) ADXL345: [ 6] X:    -7 Y:    -7 Z:   265[0m
[0;32mI (789) ADXL345: [ 7] X:    -8 Y:    -3 Z:   264[0m
[0;32mI (789) ADXL345: [ 8] X:    -9 Y:    -5 Z:   265[0m
[0;32mI (799) ADXL345: [ 9] X:    -9 Y:    -5 Z:   263[0m
[0;32mI (799) ADXL345: [10] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (809) ADXL345: [11] X:    -5 Y:    -7 Z:   262[0m
[0;32mI (809) ADXL345: [12] X:    -6 Y:    -6 Z:   264[0m
[0;32mI (819) ADXL345: [13] X:    -7 Y:    -5 Z:   265[0m
[0;32mI (819) ADXL345: [14] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (829) ADXL345: [15] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (829) ADXL345: [ 0] X:    -8 Y:    -7 Z:   264[0m
[0;32mI (839) ADXL345: [ 1] X:    -7 Y:    -6 Z:   265[0m
[0;32mI (839) ADXL345: [ 2] X:    -9 Y:    -7 Z:   259[0m
[0;32mI (849) ADXL345: [ 3] X:    -3 Y:    -3 Z:   261[0m
[0;32mI (849) ADXL345: [ 4] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (859) ADXL345: [ 5] X:    -7 Y:    -2 Z:   263[0m
[0;32mI (859) ADXL345: [ 6] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (869) ADXL345: [ 7] X:    -5 Y:    -7 Z:   261[0m
[0;32mI (879) ADXL345: [ 8] X:    -7 Y:    -6 Z:   266[0m
[0;32mI (879) ADXL345: [ 9] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (889) ADXL345: [10] X:    -8 Y:    -3 Z:   263[0m
[0;32mI (889) ADXL345: [11] X:    -7 Y:    -2 Z:   265[0m
[0;32mI (899) ADXL345: [12] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (899) ADXL345: [13] X:    -9 Y:    -7 Z:   264[0m
[0;32mI (909) ADXL345: [14] X:    -7 Y:    -4 Z:   266[0m
[0;32mI (909) ADXL345: [15] X:    -9 Y:    -6 Z:   263[0m
[0;32mI (919) ADXL345: [ 0] X:    -5 Y:    -6 Z:   264[0m
[0;32mI (919) ADXL345: [ 1] X:    -6 Y:    -8 Z:   263[0m
[0;32mI (929) ADXL345: [ 2] X:    -5 Y:    -4 Z:   262[0m
[0;32mI (929) ADXL345: [ 3] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (939) ADXL345: [ 4] X:    -6 Y:    -6 Z:   264[0m
[0;32mI (939) ADXL345: [ 5] X:   -10 Y:    -3 Z:   264[0m
[0;32mI (949) ADXL345: [ 6] X:    -6 Y:    -8 Z:   261[0m
[0;32mI (949) ADXL345: [ 7] X:    -9 Y:    -6 Z:   265[0m
[0;32mI (959) ADXL345: [ 8] X:    -7 Y:    -5 Z:   267[0m
[0;32mI (969) ADXL345: [ 9] X:    -9 Y:    -5 Z:   265[0m
[0;32mI (969) ADXL345: [10] X:    -8 Y:    -8 Z:   266[0m
[0;32mI (979) ADXL345: [11] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (979) ADXL345: [12] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (989) ADXL345: [13] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (989) ADXL345: [14] X:    -6 Y:    -5 Z:   267[0m
[0;32mI (999) ADXL345: [15] X:    -6 Y:    -7 Z:   263[0m
[0;32mI (999) ADXL345: [ 0] X:    -5 Y:    -5 Z:   263[0m
[0;32mI (1009) ADXL345: [ 1] X:    -6 Y:    -4 Z:   262[0m
[0;32mI (1009) ADXL345: [ 2] X:    -8 Y:    -6 Z:   265[0m
[0;32mI (1019) ADXL345: [ 3] X:    -7 Y:    -6 Z:   266[0m
[0;32mI (1019) ADXL345: [ 4] X:    -9 Y:    -4 Z:   259[0m
[0;32mI (1029) ADXL345: [ 5] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (1029) ADXL345: [ 6] X:    -4 Y:    -8 Z:   263[0m
[0;32mI (1039) ADXL345: [ 7] X:    -7 Y:    -6 Z:   262[0m
[0;32mI (1049) ADXL345: [ 8] X:    -7 Y:    -7 Z:   263[0m
[0;32mI (1049) ADXL345: [ 9] X:    -7 Y:    -4 Z:   266[0m
[0;32mI (1059) ADXL345: [10] X:    -5 Y:    -5 Z:   265[0m
[0;32mI (1059) ADXL345: [11] X:   -10 Y:    -6 Z:   263[0m
[0;32mI (1069) ADXL345: [12] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (1069) ADXL345: [13] X:    -6 Y:    -3 Z:   262[0m
[0;32mI (1079) ADXL345: [14] X:    -6 Y:    -6 Z:   264[0m
[0;32mI (1079) ADXL345: [15] X:   -10 Y:    -3 Z:   264[0m
[0;32mI (1089) ADXL345: [ 0] X:    -7 Y:    -4 Z:   261[0m
[0;32mI (1089) ADXL345: [ 1] X:    -8 Y:    -4 Z:   265[0m
[0;32mI (1099) ADXL345: [ 2] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (1099) ADXL345: [ 3] X:    -6 Y:    -3 Z:   262[0m
[0;32mI (1109) ADXL345: [ 4] X:    -5 Y:    -9 Z:   265[0m
[0;32mI (1119) ADXL345: [ 5] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (1119) ADXL345: [ 6] X:    -7 Y:    -3 Z:   263[0m
[0;32mI (1129) ADXL345: [ 7] X:    -6 Y:    -6 Z:   265[0m
[0;32mI (1129) ADXL345: [ 8] X:    -7 Y:    -4 Z:   265[0m
[0;32mI (1139) ADXL345: [ 9] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (1139) ADXL345: [10] X:    -9 Y:    -6 Z:   261[0m
[0;32mI (1149) ADXL345: [11] X:    -7 Y:    -6 Z:   262[0m
[0;32mI (1149) ADXL345: [12] X:    -8 Y:    -3 Z:   262[0m
[0;32mI (1159) ADXL345: [13] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (1159) ADXL345: [14] X:    -7 Y:    -5 Z:   261[0m
[0;32mI (1169) ADXL345: [15] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (1169) ADXL345: [ 0] X:    -6 Y:    -5 Z:   262[0m
[0;32mI (1179) ADXL345: [ 1] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (1189) ADXL345: [ 2] X:    -7 Y:    -6 Z:   266[0m
[0;32mI (1189) ADXL345: [ 3] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (1199) ADXL345: [ 4] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (1199) ADXL345: [ 5] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (1209) ADXL345: [ 6] X:    -8 Y:    -7 Z:   266[0m
[0;32mI (1209) ADXL345: [ 7] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (1219) ADXL345: [ 8] X:    -8 Y:    -3 Z:   263[0m
[0;32mI (1219) ADXL345: [ 9] X:    -9 Y:    -4 Z:   263[0m
[0;32mI (1229) ADXL345: [10] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (1229) ADXL345: [11] X:    -7 Y:    -6 Z:   265[0m
[0;32mI (1239) ADXL345: [12] X:    -9 Y:    -6 Z:   265[0m
[0;32mI (1239) ADXL345: [13] X:   -11 Y:    -5 Z:   263[0m
[0;32mI (1249) ADXL345: [14] X:    -9 Y:    -3 Z:   265[0m
[0;32mI (1259) ADXL345: [15] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (1259) ADXL345: [ 0] X:    -8 Y:    -6 Z:   265[0m
[0;32mI (1269) ADXL345: [ 1] X:    -5 Y:    -3 Z:   260[0m
[0;32mI (1269) ADXL345: [ 2] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (1279) ADXL345: [ 3] X:    -5 Y:    -4 Z:   268[0m
[0;32mI (1279) ADXL345: [ 4] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (1289) ADXL345: [ 5] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (1289) ADXL345: [ 6] X:    -8 Y:    -4 Z:   262[0m
[0;32mI (1299) ADXL345: [ 7] X:    -9 Y:    -7 Z:   266[0m
[0;32mI (1299) ADXL345: [ 8] X:    -5 Y:    -8 Z:   262[0m
[0;32mI (1309) ADXL345: [ 9] X:    -5 Y:    -6 Z:   263[0m
[0;32mI (1309) ADXL345: [10] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (1319) ADXL345: [11] X:   -10 Y:    -7 Z:   265[0m
[0;32mI (1329) ADXL345: [12] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (1329) ADXL345: [13] X:    -9 Y:    -3 Z:   265[0m
[0;32mI (1339) ADXL345: [14] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (1339) ADXL345: [15] X:    -8 Y:    -3 Z:   264[0m
[0;32mI (1349) ADXL345: [ 0] X:    -9 Y:    -4 Z:   266[0m
[0;32mI (1349) ADXL345: [ 1] X:   -10 Y:    -2 Z:   261[0m
[0;32mI (1359) ADXL345: [ 2] X:   -10 Y:    -3 Z:   264[0m
[0;32mI (1359) ADXL345: [ 3] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (1369) ADXL345: [ 4] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (1369) ADXL345: [ 5] X:    -7 Y:    -4 Z:   261[0m
[0;32mI (1379) ADXL345: [ 6] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (1379) ADXL345: [ 7] X:    -5 Y:    -4 Z:   263[0m
[0;32mI (1389) ADXL345: [ 8] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (1399) ADXL345: [ 9] X:    -7 Y:    -3 Z:   260[0m
[0;32mI (1399) ADXL345: [10] X:    -9 Y:    -6 Z:   261[0m
[0;32mI (1409) ADXL345: [11] X:    -5 Y:    -5 Z:   265[0m
[0;32mI (1409) ADXL345: [12] X:    -9 Y:    -5 Z:   263[0m
[0;32mI (1419) ADXL345: [13] X:    -8 Y:    -7 Z:   265[0m
[0;32mI (1419) ADXL345: [14] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (1429) ADXL345: [15] X:    -8 Y:    -4 Z:   261[0m
[0;32mI (1429) ADXL345: [ 0] X:    -7 Y:    -3 Z:   264[0m
[0;32mI (1439) ADXL345: [ 1] X:    -9 Y:    -4 Z:   265[0m
[0;32mI (1439) ADXL345: [ 2] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (1449) ADXL345: [ 3] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (1449) ADXL345: [ 4] X:    -8 Y:    -7 Z:   263[0m
[0;32mI (1459) ADXL345: [ 5] X:    -8 Y:    -4 Z:   265[0m
[0;32mI (1469) ADXL345: [ 6] X:    -9 Y:    -3 Z:   261[0m
[0;32mI (1469) ADXL345: [ 7] X:    -8 Y:    -2 Z:   262[0m
[0;32mI (1479) ADXL345: [ 8] X:    -6 Y:    -7 Z:   266[0m
[0;32mI (1479) ADXL345: [ 9] X:    -7 Y:    -3 Z:   264[0m
[0;32mI (1489) ADXL345: [10] X:    -8 Y:    -4 Z:   262[0m
[0;32mI (1489) ADXL345: [11] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (1499) ADXL345: [12] X:   -10 Y:    -3 Z:   262[0m
[0;32mI (1499) ADXL345: [13] X:    -8 Y:    -7 Z:   264[0m
[0;32mI (1509) ADXL345: [14] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (1509) ADXL345: [15] X:    -5 Y:    -6 Z:   263[0m
[0;32mI (1519) ADXL345: [ 0] X:    -7 Y:    -5 Z:   265[0m
[0;32mI (1519) ADXL345: [ 1] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (1529) ADXL345: [ 2] X:    -6 Y:    -7 Z:   264[0m
[0;32mI (1539) ADXL345: [ 3] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (1539) ADXL345: [ 4] X:    -9 Y:    -3 Z:   264[0m
[0;32mI (1549) ADXL345: [ 5] X:    -7 Y:    -6 Z:   261[0m
[0;32mI (1549) ADXL345: [ 6] X:    -8 Y:    -1 Z:   265[0m
[0;32mI (1559) ADXL345: [ 7] X:    -7 Y:    -6 Z:   262[0m
[0;32mI (1559) ADXL345: [ 8] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (1569) ADXL345: [ 9] X:    -8 Y:    -2 Z:   265[0m
[0;32mI (1569) ADXL345: [10] X:    -8 Y:    -6 Z:   266[0m
[0;32mI (1579) ADXL345: [11] X:    -6 Y:    -4 Z:   263[0m
[0;32mI (1579) ADXL345: [12] X:    -9 Y:    -3 Z:   263[0m
[0;32mI (1589) ADXL345: [13] X:    -4 Y:    -6 Z:   263[0m
[0;32mI (1589) ADXL345: [14] X:    -4 Y:    -6 Z:   262[0m
[0;32mI (1599) ADXL345: [15] X:    -7 Y:    -6 Z:   266[0m
[0;32mI (1609) ADXL345: [ 0] X:    -6 Y:    -7 Z:   262[0m
[0;32mI (1609) ADXL345: [ 1] X:    -9 Y:    -8 Z:   264[0m
[0;32mI (1619) ADXL345: [ 2] X:    -7 Y:    -5 Z:   261[0m
[0;32mI (1619) ADXL345: [ 3] X:    -8 Y:    -7 Z:   263[0m
[0;32mI (1629) ADXL345: [ 4] X:    -5 Y:    -7 Z:   264[0m
[0;32mI (1629) ADXL345: [ 5] X:    -6 Y:    -7 Z:   266[0m
[0;32mI (1639) ADXL345: [ 6] X:    -8 Y:    -6 Z:   262[0m
[0;32mI (1639) ADXL345: [ 7] X:    -9 Y:    -4 Z:   262[0m
[0;32mI (1649) ADXL345: [ 8] X:    -4 Y:    -8 Z:   262[0m
[0;32mI (1649) ADXL345: [ 9] X:    -8 Y:    -5 Z:   261[0m
[0;32mI (1659) ADXL345: [10] X:   -10 Y:    -6 Z:   266[0m
[0;32mI (1659) ADXL345: [11] X:    -6 Y:    -4 Z:   263[0m
[0;32mI (1669) ADXL345: [12] X:    -9 Y:    -5 Z:   262[0m
[0;32mI (1679) ADXL345: [13] X:    -7 Y:    -5 Z:   266[0m
[0;32mI (1679) ADXL345: [14] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (1689) ADXL345: [15] X:    -8 Y:    -4 Z:   261[0m
[0;32mI (1689) ADXL345: [ 0] X:    -7 Y:    -5 Z:   262[0m
[0;32mI (1699) ADXL345: [ 1] X:    -6 Y:    -4 Z:   263[0m
[0;32mI (1699) ADXL345: [ 2] X:    -8 Y:    -3 Z:   265[0m
[0;32mI (1709) ADXL345: [ 3] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (1709) ADXL345: [ 4] X:   -11 Y:    -5 Z:   263[0m
[0;32mI (1719) ADXL345: [ 5] X:    -8 Y:    -5 Z:   261[0m
[0;32mI (1719) ADXL345: [ 6] X:    -9 Y:    -7 Z:   264[0m
[0;32mI (1729) ADXL345: [ 7] X:    -8 Y:    -7 Z:   263[0m
[0;32mI (1729) ADXL345: [ 8] X:    -6 Y:    -6 Z:   263[0m
[0;32mI (1739) ADXL345: [ 9] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (1749) ADXL345: [10] X:   -10 Y:    -5 Z:   263[0m
[0;32mI (1749) ADXL345: [11] X:    -8 Y:    -5 Z:   265[0m
[0;32mI (1759) ADXL345: [12] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (1759) ADXL345: [13] X:    -6 Y:    -5 Z:   265[0m
[0;32mI (1769) ADXL345: [14] X:    -9 Y:    -7 Z:   265[0m
[0;32mI (1769) ADXL345: [15] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (1779) ADXL345: [ 0] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (1779) ADXL345: [ 1] X:    -7 Y:    -6 Z:   265[0m
[0;32mI (1789) ADXL345: [ 2] X:    -8 Y:    -6 Z:   265[0m
[0;32mI (1789) ADXL345: [ 3] X:   -10 Y:    -4 Z:   262[0m
[0;32mI (1799) ADXL345: [ 4] X:    -7 Y:    -6 Z:   266[0m
[0;32mI (1799) ADXL345: [ 5] X:    -8 Y:    -3 Z:   265[0m
[0;32mI (1809) ADXL345: [ 6] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (1819) ADXL345: [ 7] X:    -7 Y:    -7 Z:   265[0m
[0;32mI (1819) ADXL345: [ 8] X:    -8 Y:    -5 Z:   261[0m
[0;32mI (1829) ADXL345: [ 9] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (1829) ADXL345: [10] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (1839) ADXL345: [11] X:   -10 Y:    -4 Z:   264[0m
[0;32mI (1839) ADXL345: [12] X:    -5 Y:    -4 Z:   261[0m
[0;32mI (1849) ADXL345: [13] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (1849) ADXL345: [14] X:   -10 Y:    -2 Z:   260[0m
[0;32mI (1859) ADXL345: [15] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (1859) ADXL345: [ 0] X:    -8 Y:    -5 Z:   265[0m
[0;32mI (1869) ADXL345: [ 1] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (1869) ADXL345: [ 2] X:    -8 Y:    -8 Z:   264[0m
[0;32mI (1879) ADXL345: [ 3] X:    -8 Y:    -3 Z:   263[0m
[0;32mI (1889) ADXL345: [ 4] X:    -4 Y:    -7 Z:   265[0m
[0;32mI (1889) ADXL345: [ 5] X:   -10 Y:    -6 Z:   264[0m
[0;32mI (1899) ADXL345: [ 6] X:   -10 Y:    -6 Z:   262[0m
[0;32mI (1899) ADXL345: [ 7] X:    -8 Y:    -7 Z:   265[0m
[0;32mI (1909) ADXL345: [ 8] X:    -7 Y:    -4 Z:   263[0m
[0;32mI (1909) ADXL345: [ 9] X:    -9 Y:    -6 Z:   262[0m
[0;32mI (1919) ADXL345: [10] X:    -7 Y:    -7 Z:   262[0m
[0;32mI (1919) ADXL345: [11] X:    -9 Y:    -3 Z:   261[0m
[0;32mI (1929) ADXL345: [12] X:    -9 Y:    -5 Z:   267[0m
[0;32mI (1929) ADXL345: [13] X:   -10 Y:    -1 Z:   261[0m
[0;32mI (1939) ADXL345: [14] X:    -8 Y:    -4 Z:   266[0m
[0;32mI (1939) ADXL345: [15] X:    -6 Y:    -3 Z:   263[0m
[0;32mI (1949) ADXL345: [ 0] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (1949) ADXL345: [ 1] X:    -8 Y:    -7 Z:   263[0m
[0;32mI (1959) ADXL345: [ 2] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (1969) ADXL345: [ 3] X:    -5 Y:    -4 Z:   264[0m
[0;32mI (1969) ADXL345: [ 4] X:    -6 Y:    -6 Z:   267[0m
[0;32mI (1979) ADXL345: [ 5] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (1979) ADXL345: [ 6] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (1989) ADXL345: [ 7] X:    -7 Y:    -3 Z:   265[0m
[0;32mI (1989) ADXL345: [ 8] X:    -6 Y:    -7 Z:   265[0m
[0;32mI (1999) ADXL345: [ 9] X:    -8 Y:    -7 Z:   263[0m
[0;32mI (1999) ADXL345: [10] X:   -10 Y:    -2 Z:   263[0m
[0;32mI (2009) ADXL345: [11] X:    -8 Y:    -7 Z:   262[0m
[0;32mI (2009) ADXL345: [12] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (2019) ADXL345: [13] X:    -7 Y:    -7 Z:   263[0m
[0;32mI (2019) ADXL345: [14] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (2029) ADXL345: [15] X:   -10 Y:    -3 Z:   261[0m
[0;32mI (2039) ADXL345: [ 0] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (2039) ADXL345: [ 1] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (2049) ADXL345: [ 2] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (2049) ADXL345: [ 3] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (2059) ADXL345: [ 4] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (2059) ADXL345: [ 5] X:    -5 Y:    -7 Z:   263[0m
[0;32mI (2069) ADXL345: [ 6] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (2069) ADXL345: [ 7] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (2079) ADXL345: [ 8] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (2079) ADXL345: [ 9] X:    -8 Y:    -5 Z:   264[0m
[0;32mI (2089) ADXL345: [10] X:    -6 Y:    -6 Z:   263[0m
[0;32mI (2089) ADXL345: [11] X:    -7 Y:    -7 Z:   263[0m
[0;32mI (2099) ADXL345: [12] X:   -10 Y:    -4 Z:   264[0m
[0;32mI (2109) ADXL345: [13] X:    -7 Y:    -6 Z:   261[0m
[0;32mI (2109) ADXL345: [14] X:    -7 Y:    -4 Z:   264[0m
[0;32mI (2119) ADXL345: [15] X:   -10 Y:    -5 Z:   264[0m
[0;32mI (2119) ADXL345: [ 0] X:    -5 Y:    -6 Z:   262[0m
[0;32mI (2129) ADXL345: [ 1] X:    -8 Y:    -4 Z:   264[0m
[0;32mI (2129) ADXL345: [ 2] X:    -8 Y:    -5 Z:   266[0m
[0;32mI (2139) ADXL345: [ 3] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (2139) ADXL345: [ 4] X:    -6 Y:    -4 Z:   263[0m
[0;32mI (2149) ADXL345: [ 5] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (2149) ADXL345: [ 6] X:    -7 Y:    -5 Z:   265[0m
[0;32mI (2159) ADXL345: [ 7] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (2159) ADXL345: [ 8] X:    -7 Y:    -4 Z:   261[0m
[0;32mI (2169) ADXL345: [ 9] X:    -7 Y:    -4 Z:   265[0m
[0;32mI (2179) ADXL345: [10] X:    -8 Y:    -6 Z:   266[0m
[0;32mI (2179) ADXL345: [11] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (2189) ADXL345: [12] X:    -9 Y:    -6 Z:   261[0m
[0;32mI (2189) ADXL345: [13] X:   -10 Y:    -5 Z:   262[0m
[0;32mI (2199) ADXL345: [14] X:    -6 Y:    -5 Z:   264[0m
[0;32mI (2199) ADXL345: [15] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (2209) ADXL345: [ 0] X:    -8 Y:    -1 Z:   263[0m
[0;32mI (2209) ADXL345: [ 1] X:    -9 Y:    -5 Z:   264[0m
[0;32mI (2219) ADXL345: [ 2] X:    -7 Y:    -7 Z:   264[0m
[0;32mI (2219) ADXL345: [ 3] X:    -5 Y:    -4 Z:   265[0m
[0;32mI (2229) ADXL345: [ 4] X:   -11 Y:    -3 Z:   261[0m
[0;32mI (2229) ADXL345: [ 5] X:    -9 Y:    -5 Z:   265[0m
[0;32mI (2239) ADXL345: [ 6] X:    -4 Y:    -6 Z:   265[0m
[0;32mI (2249) ADXL345: [ 7] X:    -7 Y:    -3 Z:   263[0m
[0;32mI (2249) ADXL345: [ 8] X:   -10 Y:    -6 Z:   261[0m
[0;32mI (2259) ADXL345: [ 9] X:    -6 Y:    -4 Z:   264[0m
[0;32mI (2259) ADXL345: [10] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (2269) ADXL345: [11] X:    -8 Y:    -5 Z:   266[0m
[0;32mI (2269) ADXL345: [12] X:    -5 Y:    -6 Z:   262[0m
[0;32mI (2279) ADXL345: [13] X:    -7 Y:    -8 Z:   263[0m
[0;32mI (2279) ADXL345: [14] X:    -7 Y:    -7 Z:   261[0m
[0;32mI (2289) ADXL345: [15] X:    -6 Y:    -7 Z:   261[0m
[0;32mI (2289) ADXL345: [ 0] X:    -9 Y:    -7 Z:   263[0m
[0;32mI (2299) ADXL345: [ 1] X:    -7 Y:    -7 Z:   262[0m
[0;32mI (2299) ADXL345: [ 2] X:    -6 Y:    -6 Z:   263[0m
[0;32mI (2309) ADXL345: [ 3] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (2319) ADXL345: [ 4] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (2319) ADXL345: [ 5] X:    -7 Y:    -6 Z:   262[0m
[0;32mI (2329) ADXL345: [ 6] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (2329) ADXL345: [ 7] X:    -8 Y:    -6 Z:   265[0m
[0;32mI (2339) ADXL345: [ 8] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (2339) ADXL345: [ 9] X:    -8 Y:    -6 Z:   264[0m
[0;32mI (2349) ADXL345: [10] X:    -9 Y:    -6 Z:   264[0m
[0;32mI (2349) ADXL345: [11] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (2359) ADXL345: [12] X:    -6 Y:    -6 Z:   263[0m
[0;32mI (2359) ADXL345: [13] X:    -9 Y:    -4 Z:   265[0m
[0;32mI (2369) ADXL345: [14] X:    -8 Y:    -4 Z:   265[0m
[0;32mI (2369) ADXL345: [15] X:    -6 Y:    -3 Z:   263[0m
[0;32mI (2379) ADXL345: [ 0] X:    -8 Y:    -7 Z:   262[0m
[0;32mI (2389) ADXL345: [ 1] X:    -6 Y:    -2 Z:   260[0m
[0;32mI (2389) ADXL345: [ 2] X:    -7 Y:    -3 Z:   260[0m
[0;32mI (2399) ADXL345: [ 3] X:    -4 Y:    -6 Z:   262[0m
[0;32mI (2399) ADXL345: [ 4] X:    -7 Y:    -5 Z:   263[0m
[0;32mI (2409) ADXL345: [ 5] X:    -9 Y:    -7 Z:   264[0m
[0;32mI (2409) ADXL345: [ 6] X:    -8 Y:    -4 Z:   261[0m
[0;32mI (2419) ADXL345: [ 7] X:    -7 Y:    -3 Z:   264[0m
[0;32mI (2419) ADXL345: [ 8] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (2429) ADXL345: [ 9] X:    -4 Y:    -6 Z:   262[0m
[0;32mI (2429) ADXL345: [10] X:    -5 Y:    -5 Z:   264[0m
[0;32mI (2439) ADXL345: [11] X:    -5 Y:    -7 Z:   263[0m
[0;32mI (2439) ADXL345: [12] X:    -7 Y:    -7 Z:   264[0m
[0;32mI (2449) ADXL345: [13] X:    -9 Y:    -4 Z:   265[0m
[0;32mI (2459) ADXL345: [14] X:    -7 Y:    -6 Z:   264[0m
[0;32mI (2459) ADXL345: [15] X:    -6 Y:    -7 Z:   261[0m
[0;32mI (2469) ADXL345: [ 0] X:    -9 Y:    -2 Z:   263[0m
[0;32mI (2469) ADXL345: [ 1] X:    -8 Y:    -3 Z:   261[0m
[0;32mI (2479) ADXL345: [ 2] X:   -11 Y:    -6 Z:   264[0m
[0;32mI (2479) ADXL345: [ 3] X:    -6 Y:    -4 Z:   263[0m
[0;32mI (2489) ADXL345: [ 4] X:    -5 Y:    -5 Z:   263[0m
[0;32mI (2489) ADXL345: [ 5] X:    -6 Y:    -1 Z:   264[0m
[0;32mI (2499) ADXL345: [ 6] X:    -7 Y:    -7 Z:   266[0m
[0;32mI (2499) ADXL345: [ 7] X:    -8 Y:    -9 Z:   266[0m
[0;32mI (2509) ADXL345: [ 8] X:    -6 Y:    -9 Z:   264[0m
[0;32mI (2509) ADXL345: [ 9] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (2519) ADXL345: [10] X:    -9 Y:    -3 Z:   262[0m
[0;32mI (2529) ADXL345: [11] X:    -6 Y:    -7 Z:   262[0m
[0;32mI (2529) ADXL345: [12] X:    -4 Y:    -7 Z:   265[0m
[0;32mI (2539) ADXL345: [13] X:    -8 Y:    -7 Z:   261[0m
[0;32mI (2539) ADXL345: [14] X:    -5 Y:    -8 Z:   264[0m
[0;32mI (2549) ADXL345: [15] X:    -6 Y:    -5 Z:   263[0m
[0;32mI (2549) ADXL345: [ 0] X:    -7 Y:    -6 Z:   263[0m
[0;32mI (2559) ADXL345: [ 1] X:    -8 Y:    -4 Z:   263[0m
[0;32mI (2559) ADXL345: [ 2] X:   -10 Y:    -3 Z:   261[0m
[0;32mI (2569) ADXL345: [ 3] X:    -5 Y:    -7 Z:   262[0m
[0;32mI (2569) ADXL345: [ 4] X:    -7 Y:    -5 Z:   260[0m
[0;32mI (2579) ADXL345: [ 5] X:    -8 Y:    -6 Z:   263[0m
[0;32mI (2579) ADXL345: [ 6] X:   -10 Y:    -2 Z:   263[0m
[0;32mI (2589) ADXL345: [ 7] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (2599) ADXL345: [ 8] X:    -8 Y:    -5 Z:   262[0m
[0;32mI (2599) ADXL345: [ 9] X:    -8 Y:    -2 Z:   263[0m
[0;32mI (2609) ADXL345: [10] X:    -9 Y:    -6 Z:   263[0m
[0;32mI (2609) ADXL345: [11] X:   -10 Y:    -5 Z:   264[0m
[0;32mI (2619) ADXL345: [12] X:    -7 Y:    -3 Z:   263[0m
[0;32mI (2619) ADXL345: [13] X:    -6 Y:    -6 Z:   265[0m
[0;32mI (2629) ADXL345: [14] X:    -7 Y:    -5 Z:   264[0m
[0;32mI (2629) ADXL345: [15] X:   -10 Y:    -6 Z:   266[0m
[0;32mI (2639) ADXL345: [ 0] X:    -8 Y:    -7 Z:   265[0m
[0;32mI (2639) ADXL345: [ 1] X:    -7 Y:    -8 Z:   263[0m
[0;32mI (2649) ADXL345: [ 2] X:    -7 Y:    -7 Z:   261[0m
[0;32mI (2649) ADXL345: [ 3] X:    -7 Y:    -4 Z:   262[0m
[0;32mI (2659) ADXL345: [ 4] X:    -9 Y:    -5 Z:   265[0m
[0;32mI (2669) ADXL345: [ 5] X:    -9 Y:    -2 Z:   264[0m
[0;32mI (2669) ADXL345: [ 6] X:    -8 Y:    -2 Z:   265[0m
[0;32mI (2679) ADXL345: [ 7] X:    -6 Y:    -5 Z:   265[0m
[0;32mI (2679) ADXL345: [ 8] X:    -8 Y:    -5 Z:   263[0m
[0;32mI (2689) ADXL345: [ 9] X:    -9 Y:    -4 Z:   264[0m
[0;32mI (2689) ADXL345: [10] X:    -6 Y:    -3 Z:   263[0m
[0;32mI (2699) ADXL345: [11] X:    -9 Y:    -4 Z:   262[0m
[0;32mI (2699) ADXL345: [12] X:    -9 Y:    -7 Z:   264[0m
[0;32mI (2709) ADXL345: [13] X:   -10 Y:    -5 Z:   262[0m
[0;32mI (2709) ADXL345: [14] X:    -9 Y:    -4 Z:   266[0m
[0;32mI (2719) ADXL345: [15] X:    -8 Y:    -2 Z:   263[0m
[0;32mI (2719) ADXL345: [ 0] X:    -4 Y:    -7 Z:   260[0m
[0;32mI (2729) ADXL345: [ 1] X:    -9 Y:    -5 Z:   262[0m
[0;32mI (2739) ADXL345: [ 2] X:    -7 Y:    -6 Z:   263[0m
