������] X:   104 Y:   231 Z:   -14[0m
[0;32mI (761) DUAL_ADXL345:   [2] X:    99 Y:   224 Z:   -15[0m
[0;32mI (761) DU data (timestamp: 47, samples: 16):[0m
[0;32mI (791) DUAL_ADXL345:   [0] X:   102 Y:   226 Z:   -14[0m
[0;32mI (791) DUAL_ADXL345:   [1] X:   105 Y:   221 Z:   -14[0m
[0;32mI (ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (53) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0bc54h ( 48212) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001bc7c vaddr=3fc93800 size=02a94h ( 10900) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e718 vaddr=40374000 size=01900h (  6400) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a478h (107640) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a4a0 vaddr=40375900 size=0dee0h ( 57056) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (189) cpu_start: Pro cpu start user code[0m
[0;32mI (189) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (189) app_init: Application information:[0m
[0;32mI (192) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      f08ec8f-dirty[0m
[0;32mI (202) app_init: Compile time:     May 27 2025 16:21:18[0m
[0;32mI (208) app_init: ELF file SHA256:  5259d108c...[0m
[0;32mI (213) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (218) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (223) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (240) heap_init: At 3FC96B60 len 00052BB0 (330 KiB): RAM[0m
[0;32mI (246) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (252) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (258) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (265) spi_flash: detected chip: gd[0m
[0;32mI (269) spi_flash: flash io: dio[0m
[0;33mW (272) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (286) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (296) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (303) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (311) main_task: Started on CPU0[0m
[0;32mI (321) main_task: Calling app_main()[0m
[0;32mI (321) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (321) DUAL_ADXL345: I2C Bus 0 initialized (SDA=17, SCL=15)[0m
[0;32mI (331) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (341) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (341) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;31mE (351) DUAL_ADXL345: No ADXL345 found on Sensor1_Bus0 (DEVID=0x00)[0m
[0;31mE (361) DUAL_ADXL345: Failed to initialize Sensor1_Bus0, terminating task[0m
[0;32mI (361) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (371) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0;32mI (371) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (381) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @400Hz, burst=16[0m
[0;32mI (391) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=17, SCL=15), Address=0x53[0m
[0;32mI (401) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;32mI (431) DUAL_ADXL345: Sensor 2 data (timestamp: 11, samples: 16):[0m
[0;32mI (431) DUAL_ADXL345:   [0] X:    96 Y:   212 Z:   -10[0m
[0;32mI (431) DUAL_ADXL345:   [1] X:   105 Y:   228 Z:   -11[0m
[0;32mI (431) DUAL_ADXL345:   [2] X:   104 Y:   231 Z:   -13[0m
[0;32mI (441) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (471) DUAL_ADXL345: Sensor 2 data (timestamp: 15, samples: 16):[0m
[0;32mI (471) DUAL_ADXL345:   [0] X:   100 Y:   227 Z:   -12[0m
[0;32mI (471) DUAL_ADXL345:   [1] X:   101 Y:   225 Z:   -18[0m
[0;32mI (471) DUAL_ADXL345:   [2] X:   102 Y:   228 Z:   -11[0m
[0;32mI (481) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (511) DUAL_ADXL345: Sensor 2 data (timestamp: 19, samples: 16):[0m
[0;32mI (511) DUAL_ADXL345:   [0] X:   103 Y:   233 Z:   -13[0m
[0;32mI (511) DUAL_ADXL345:   [1] X:   104 Y:   228 Z:   -16[0m
[0;32mI (521) DUAL_ADXL345:   [2] X:    96 Y:   226 Z:   -12[0m
[0;32mI (521) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (551) DUAL_ADXL345: Sensor 2 data (timestamp: 23, samples: 16):[0m
[0;32mI (551) DUAL_ADXL345:   [0] X:   104 Y:   227 Z:   -12[0m
[0;32mI (551) DUAL_ADXL345:   [1] X:   107 Y:   231 Z:    -9[0m
[0;32mI (561) DUAL_ADXL345:   [2] X:   105 Y:   225 Z:   -14[0m
[0;32mI (561) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (591) DUAL_ADXL345: Sensor 2 data (timestamp: 27, samples: 16):[0m
[0;32mI (591) DUAL_ADXL345:   [0] X:   102 Y:   222 Z:   -18[0m
[0;32mI (591) DUAL_ADXL345:   [1] X:   102 Y:   225 Z:   -15[0m
[0;32mI (601) DUAL_ADXL345:   [2] X:   105 Y:   228 Z:   -15[0m
[0;32mI (601) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (631) DUAL_ADXL345: Sensor 2 data (timestamp: 31, samples: 16):[0m
[0;32mI (631) DUAL_ADXL345:   [0] X:   106 Y:   231 Z:   -12[0m
[0;32mI (631) DUAL_ADXL345:   [1] X:   108 Y:   230 Z:   -19[0m
[0;32mI (641) DUAL_ADXL345:   [2] X:    98 Y:   223 Z:   -14[0m
[0;32mI (641) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (671) DUAL_ADXL345: Sensor 2 data (timestamp: 35, samples: 16):[0m
[0;32mI (671) DUAL_ADXL345:   [0] X:   100 Y:   224 Z:    -9[0m
[0;32mI (671) DUAL_ADXL345:   [1] X:   106 Y:   233 Z:    -8[0m
[0;32mI (681) DUAL_ADXL345:   [2] X:   100 Y:   224 Z:   -18[0m
[0;32mI (681) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (711) DUAL_ADXL345: Sensor 2 data (timestamp: 39, samples: 16):[0m
[0;32mI (711) DUAL_ADXL345:   [0] X:   101 Y:   225 Z:   -15[0m
[0;32mI (711) DUAL_ADXL345:   [1] X:   100 Y:   227 Z:   -14[0m
[0;32mI (721) DUAL_ADXL345:   [2] X:   101 Y:   226 Z:   -11[0m
[0;32mI (721) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (751) DUAL_ADXL345: Sensor 2 data (timestamp: 43, samples: 16):[0m
[0;32mI (751) DUAL_ADXL345:   [0] X:   104 Y:   231 Z:    -8[0m
[0;32mI (751) DUAL_ADXL345:   [1] X:   110 Y:   229 Z:   -14[0m
[0;32mI (761) DUAL_ADXL345:   [2] X:    97 Y:   227 Z:   -14[0m
[0;32mI (761) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (791) DUAL_ADXL345: Sensor 2 data (timestamp: 47, samples: 16):[0m
[0;32mI (791) DUAL_ADXL345:   [0] X:   101 Y:   228 Z:   -11[0m
[0;32mI (791) DUAL_ADXL345:   [1] X:   102 Y:   223 Z:   -15[0m
[0;32mI (801) DUAL_ADXL345:   [2] X:   108 Y:   235 Z:   -10[0m
[0;32mI (801) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (831) DUAL_ADXL345: Sensor 2 data (timestamp: 51, samples: 16):[0m
[0;32mI (831) DUAL_ADXL345:   [0] X:   100 Y:   229 Z:   -14[0m
[0;32mI (831) DUAL_ADXL345:   [1] X:   106 Y:   229 Z:   -15[0m
[0;32mI (841) DUAL_ADXL345:   [2] X:    99 Y:   225 Z:    -6[0m
[0;32mI (841) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (871) DUAL_ADXL345: Sensor 2 data (timestamp: 55, samples: 16):[0m
[0;32mI (871) DUAL_ADXL345:   [0] X:   104 Y:   235 Z:    -8[0m
[0;32mI (871) DUAL_ADXL345:   [1] X:   108 Y:   235 Z:    -6[0m
[0;32mI (881) DUAL_ADXL345:   [2] X:   102 Y:   226 Z:   -15[0m
[0;32mI (881) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (911) DUAL_ADXL345: Sensor 2 data (timestamp: 59, samples: 16):[0m
[0;32mI (911) DUAL_ADXL345:   [0] X:    99 Y:   224 Z:   -13[0m
[0;32mI (911) DUAL_ADXL345:   [1] X:    96 Y:   229 Z:   -18[0m
[0;32mI (921) DUAL_ADXL345:   [2] X:   100 Y:   233 Z:    -9[0m
[0;32mI (921) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (951) DUAL_ADXL345: Sensor 2 data (timestamp: 64, samples: 16):[0m
[0;32mI (951) DUAL_ADXL345:   [0] X:   108 Y:   228 Z:    -9[0m
[0;32mI (951) DUAL_ADXL345:   [1] X:   101 Y:   228 Z:   -16[0m
[0;32mI (961) DUAL_ADXL345:   [2] X:    95 Y:   232 Z:   -10[0m
[0;32mI (961) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (991) DUAL_ADXL345: Sensor 2 data (timestamp: 68, samples: 16):[0m
[0;32mI (991) DUAL_ADXL345:   [0] X:   105 Y:   229 Z:    -5[0m
[0;32mI (991) DUAL_ADXL345:   [1] X:   106 Y:   229 Z:   -10[0m
[0;32mI (1001) DUAL_ADXL345:   [2] X:   100 Y:   225 Z:   -16[0m
[0;32mI (1011) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1031) DUAL_ADXL345: Sensor 2 data (timestamp: 72, samples: 16):[0m
[0;32mI (1031) DUAL_ADXL345:   [0] X:   101 Y:   223 Z:   -18[0m
[0;32mI (1031) DUAL_ADXL345:   [1] X:   100 Y:   224 Z:   -15[0m
[0;32mI (1041) DUAL_ADXL345:   [2] X:   106 Y:   226 Z:    -8[0m
[0;32mI (1051) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1071) DUAL_ADXL345: Sensor 2 data (timestamp: 76, samples: 16):[0m
[0;32mI (1071) DUAL_ADXL345:   [0] X:   102 Y:   232 Z:   -13[0m
[0;32mI (1071) DUAL_ADXL345:   [1] X:   108 Y:   229 Z:   -16[0m
[0;32mI (1081) DUAL_ADXL345:   [2] X:    96 Y:   224 Z:   -11[0m
[0;32mI (1091) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1111) DUAL_ADXL345: Sensor 2 data (timestamp: 80, samples: 16):[0m
[0;32mI (1111) DUAL_ADXL345:   [0] X:   103 Y:   228 Z:   -15[0m
[0;32mI (1111) DUAL_ADXL345:   [1] X:    96 Y:   228 Z:   -11[0m
[0;32mI (1121) DUAL_ADXL345:   [2] X:   108 Y:   235 Z:   -14[0m
[0;32mI (1131) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1151) DUAL_ADXL345: Sensor 2 data (timestamp: 84, samples: 16):[0m
[0;32mI (1151) DUAL_ADXL345:   [0] X:   104 Y:   228 Z:   -15[0m
[0;32mI (1161) DUAL_ADXL345:   [1] X:   102 Y:   225 Z:   -17[0m
[0;32mI (1161) DUAL_ADXL345:   [2] X:   100 Y:   228 Z:   -10[0m
[0;32mI (1171) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1191) DUAL_ADXL345: Sensor 2 data (timestamp: 88, samples: 16):[0m
[0;32mI (1191) DUAL_ADXL345:   [0] X:   102 Y:   231 Z:   -12[0m
[0;32mI (1201) DUAL_ADXL345:   [1] X:   103 Y:   239 Z:    -7[0m
[0;32mI (1201) DUAL_ADXL345:   [2] X:    95 Y:   229 Z:   -17[0m
[0;32mI (1211) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1231) DUAL_ADXL345: Sensor 2 data (timestamp: 92, samples: 16):[0m
[0;32mI (1231) DUAL_ADXL345:   [0] X:   103 Y:   226 Z:   -13[0m
[0;32mI (1241) DUAL_ADXL345:   [1] X:   103 Y:   223 Z:   -18[0m
[0;32mI (1241) DUAL_ADXL345:   [2] X:   102 Y:   230 Z:    -8[0m
[0;32mI (1251) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1271) DUAL_ADXL345: Sensor 2 data (timestamp: 96, samples: 16):[0m
[0;32mI (1271) DUAL_ADXL345:   [0] X:   103 Y:   229 Z:    -4[0m
[0;32mI (1281) DUAL_ADXL345:   [1] X:   108 Y:   233 Z:    -5[0m
[0;32mI (1281) DUAL_ADXL345:   [2] X:    98 Y:   224 Z:   -19[0m
[0;32mI (1291) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1311) DUAL_ADXL345: Sensor 2 data (timestamp: 100, samples: 16):[0m
[0;32mI (1311) DUAL_ADXL345:   [0] X:    98 Y:   227 Z:    -9[0m
[0;32mI (1321) DUAL_ADXL345:   [1] X:   101 Y:   222 Z:   -13[0m
[0;32mI (1321) DUAL_ADXL345:   [2] X:   103 Y:   233 Z:    -7[0m
[0;32mI (1331) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1361) DUAL_ADXL345: Sensor 2 data (timestamp: 104, samples: 16):[0m
[0;32mI (1361) DUAL_ADXL345:   [0] X:   102 Y:   229 Z:   -14[0m
[0;32mI (1361) DUAL_ADXL345:   [1] X:   106 Y:   225 Z:   -16[0m
[0;32mI (1361) DUAL_ADXL345:   [2] X:    97 Y:   225 Z:   -14[0m
[0;32mI (1371) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1401) DUAL_ADXL345: Sensor 2 data (timestamp: 108, samples: 16):[0m
[0;32mI (1401) DUAL_ADXL345:   [0] X:    99 Y:   229 Z:    -8[0m
[0;32mI (1401) DUAL_ADXL345:   [1] X:   104 Y:   233 Z:    -1[0m
[0;32mI (1401) DUAL_ADXL345:   [2] X:   104 Y:   223 Z:   -16[0m
[0;32mI (1411) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1441) DUAL_ADXL345: Sensor 2 data (timestamp: 112, samples: 16):[0m
[0;32mI (1441) DUAL_ADXL345:   [0] X:   103 Y:   227 Z:   -15[0m
[0;32mI (1441) DUAL_ADXL345:   [1] X:    99 Y:   226 Z:   -18[0m
[0;32mI (1441) DUAL_ADXL345:   [2] X:   102 Y:   228 Z:   -11[0m
[0;32mI (1451) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1481) DUAL_ADXL345: Sensor 2 data (timestamp: 116, samples: 16):[0m
[0;32mI (1481) DUAL_ADXL345:   [0] X:   104 Y:   233 Z:   -12[0m
[0;32mI (1481) DUAL_ADXL345:   [1] X:   105 Y:   228 Z:   -14[0m
[0;32mI (1481) DUAL_ADXL345:   [2] X:    95 Y:   224 Z:   -18[0m
[0;32mI (1491) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1521) DUAL_ADXL345: Sensor 2 data (timestamp: 120, samples: 16):[0m
[0;32mI (1521) DUAL_ADXL345:   [0] X:   102 Y:   226 Z:   -12[0m
[0;32mI (1521) DUAL_ADXL345:   [1] X:    99 Y:   225 Z:   -15[0m
[0;32mI (1521) DUAL_ADXL345:   [2] X:   104 Y:   236 Z:    -6[0m
[0;32mI (1531) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1561) DUAL_ADXL345: Sensor 2 data (timestamp: 124, samples: 16):[0m
[0;32mI (1561) DUAL_ADXL345:   [0] X:   104 Y:   230 Z:   -15[0m
[0;32mI (1561) DUAL_ADXL345:   [1] X:   106 Y:   230 Z:   -13[0m
[0;32mI (1561) DUAL_ADXL345:   [2] X:   102 Y:   229 Z:   -10[0m
[0;32mI (1571) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1601) DUAL_ADXL345: Sensor 2 data (timestamp: 128, samples: 16):[0m
[0;32mI (1601) DUAL_ADXL345:   [0] X:   104 Y:   227 Z:    -8[0m
[0;32mI (1601) DUAL_ADXL345:   [1] X:   105 Y:   227 Z:    -8[0m
[0;32mI (1601) DUAL_ADXL345:   [2] X:    98 Y:   223 Z:   -11[0m
[0;32mI (1611) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1641) DUAL_ADXL345: Sensor 2 data (timestamp: 132, samples: 16):[0m
[0;32mI (1641) DUAL_ADXL345:   [0] X:    99 Y:   225 Z:   -15[0m
[0;32mI (1641) DUAL_ADXL345:   [1] X:    97 Y:   227 Z:   -17[0m
[0;32mI (1651) DUAL_ADXL345:   [2] X:    98 Y:   232 Z:   -13[0m
[0;32mI (1651) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1681) DUAL_ADXL345: Sensor 2 data (timestamp: 136, samples: 16):[0m
[0;32mI (1681) DUAL_ADXL345:   [0] X:   103 Y:   232 Z:    -9[0m
[0;32mI (1681) DUAL_ADXL345:   [1] X:   104 Y:   234 Z:    -4[0m
[0;32mI (1691) DUAL_ADXL345:   [2] X:   102 Y:   226 Z:   -15[0m
[0;32mI (1691) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1721) DUAL_ADXL345: Sensor 2 data (timestamp: 140, samples: 16):[0m
[0;32mI (1721) DUAL_ADXL345:   [0] X:   100 Y:   224 Z:   -12[0m
[0;32mI (1721) DUAL_ADXL345:   [1] X:    97 Y:   220 Z:   -15[0m
[0;32mI (1731) DUAL_ADXL345:   [2] X:   101 Y:   232 Z:    -9[0m
[0;32mI (1731) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1761) DUAL_ADXL345: Sensor 2 data (timestamp: 144, samples: 16):[0m
[0;32mI (1761) DUAL_ADXL345:   [0] X:   101 Y:   229 Z:   -13[0m
[0;32mI (1761) DUAL_ADXL345:   [1] X:   101 Y:   230 Z:   -14[0m
[0;32mI (1771) DUAL_ADXL345:   [2] X:    96 Y:   230 Z:   -13[0m
[0;32mI (1771) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1801) DUAL_ADXL345: Sensor 2 data (timestamp: 148, samples: 16):[0m
[0;32mI (1801) DUAL_ADXL345:   [0] X:   100 Y:   232 Z:   -10[0m
[0;32mI (1801) DUAL_ADXL345:   [1] X:   108 Y:   236 Z:   -13[0m
[0;32mI (1811) DUAL_ADXL345:   [2] X:    99 Y:   225 Z:    -8[0m
[0;32mI (1811) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1841) DUAL_ADXL345: Sensor 2 data (timestamp: 152, samples: 16):[0m
[0;32mI (1841) DUAL_ADXL345:   [0] X:   100 Y:   225 Z:   -13[0m
[0;32mI (1841) DUAL_ADXL345:   [1] X:   100 Y:   224 Z:   -17[0m
[0;32mI (1851) DUAL_ADXL345:   [2] X:   101 Y:   229 Z:    -7[0m
[0;32mI (1851) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1881) DUAL_ADXL345: Sensor 2 data (timestamp: 156, samples: 16):[0m
[0;32mI (1881) DUAL_ADXL345:   [0] X:   107 Y:   228 Z:    -8[0m
[0;32mI (1881) DUAL_ADXL345:   [1] X:   107 Y:   228 Z:    -9[0m
[0;32mI (1891) DUAL_ADXL345:   [2] X:    96 Y:   226 Z:   -16[0m
[0;32mI (1891) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1921) DUAL_ADXL345: Sensor 2 data (timestamp: 160, samples: 16):[0m
[0;32mI (1921) DUAL_ADXL345:   [0] X:    98 Y:   227 Z:   -12[0m
[0;32mI (1921) DUAL_ADXL345:   [1] X:    96 Y:   222 Z:   -12[0m
[0;32mI (1931) DUAL_ADXL345:   [2] X:   111 Y:   228 Z:   -10[0m
[0;32mI (1931) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (1961) DUAL_ADXL345: Sensor 2 data (timestamp: 164, samples: 16):[0m
[0;32mI (1961) DUAL_ADXL345:   [0] X:   103 Y:   230 Z:   -14[0m
[0;32mI (1961) DUAL_ADXL345:   [1] X:   103 Y:   227 Z:   -20[0m
[0;32mI (1971) DUAL_ADXL345:   [2] X:   100 Y:   226 Z:   -17[0m
[0;32mI (1971) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2001) DUAL_ADXL345: Sensor 2 data (timestamp: 168, samples: 16):[0m
[0;32mI (2001) DUAL_ADXL345:   [0] X:   100 Y:   230 Z:    -8[0m
[0;32mI (2001) DUAL_ADXL345:   [1] X:   104 Y:   235 Z:    -7[0m
[0;32mI (2011) DUAL_ADXL345:   [2] X:   100 Y:   225 Z:   -13[0m
[0;32mI (2011) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2041) DUAL_ADXL345: Sensor 2 data (timestamp: 172, samples: 16):[0m
[0;32mI (2041) DUAL_ADXL345:   [0] X:    98 Y:   228 Z:   -14[0m
[0;32mI (2041) DUAL_ADXL345:   [1] X:   101 Y:   224 Z:   -19[0m
[0;32mI (2051) DUAL_ADXL345:   [2] X:   104 Y:   232 Z:    -6[0m
[0;32mI (2051) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2081) DUAL_ADXL345: Sensor 2 data (timestamp: 176, samples: 16):[0m
[0;32mI (2081) DUAL_ADXL345:   [0] X:   104 Y:   236 Z:   -12[0m
[0;32mI (2081) DUAL_ADXL345:   [1] X:   103 Y:   232 Z:   -10[0m
[0;32mI (2091) DUAL_ADXL345:   [2] X:    95 Y:   225 Z:   -18[0m
[0;32mI (2091) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2121) DUAL_ADXL345: Sensor 2 data (timestamp: 181, samples: 16):[0m
[0;32mI (2121) DUAL_ADXL345:   [0] X:   101 Y:   224 Z:   -11[0m
[0;32mI (2121) DUAL_ADXL345:   [1] X:    99 Y:   221 Z:   -12[0m
[0;32mI (2131) DUAL_ADXL345:   [2] X:   105 Y:   228 Z:   -15[0m
[0;32mI (2141) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2161) DUAL_ADXL345: Sensor 2 data (timestamp: 185, samples: 16):[0m
[0;32mI (2161) DUAL_ADXL345:   [0] X:   103 Y:   226 Z:   -17[0m
[0;32mI (2161) DUAL_ADXL345:   [1] X:    99 Y:   227 Z:   -13[0m
[0;32mI (2171) DUAL_ADXL345:   [2] X:   104 Y:   228 Z:   -12[0m
[0;32mI (2181) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2201) DUAL_ADXL345: Sensor 2 data (timestamp: 189, samples: 16):[0m
[0;32mI (2201) DUAL_ADXL345:   [0] X:   101 Y:   231 Z:    -9[0m
[0;32mI (2201) DUAL_ADXL345:   [1] X:   106 Y:   230 Z:   -14[0m
[0;32mI (2211) DUAL_ADXL345:   [2] X:   102 Y:   226 Z:   -18[0m
[0;32mI (2221) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2241) DUAL_ADXL345: Sensor 2 data (timestamp: 193, samples: 16):[0m
[0;32mI (2241) DUAL_ADXL345:   [0] X:   100 Y:   226 Z:   -15[0m
[0;32mI (2241) DUAL_ADXL345:   [1] X:    97 Y:   228 Z:   -12[0m
[0;32mI (2251) DUAL_ADXL345:   [2] X:   103 Y:   221 Z:   -13[0m
[0;32mI (2261) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2281) DUAL_ADXL345: Sensor 2 data (timestamp: 197, samples: 16):[0m
[0;32mI (2281) DUAL_ADXL345:   [0] X:   102 Y:   230 Z:   -13[0m
[0;32mI (2281) DUAL_ADXL345:   [1] X:   105 Y:   231 Z:   -10[0m
[0;32mI (2291) DUAL_ADXL345:   [2] X:   101 Y:   224 Z:   -13[0m
[0;32mI (2301) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2321) DUAL_ADXL345: Sensor 2 data (timestamp: 201, samples: 16):[0m
[0;32mI (2321) DUAL_ADXL345:   [0] X:    99 Y:   227 Z:   -11[0m
[0;32mI (2331) DUAL_ADXL345:   [1] X:    93 Y:   228 Z:    -8[0m
[0;32mI (2331) DUAL_ADXL345:   [2] X:   109 Y:   229 Z:   -10[0m
[0;32mI (2341) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2361) DUAL_ADXL345: Sensor 2 data (timestamp: 205, samples: 16):[0m
[0;32mI (2361) DUAL_ADXL345:   [0] X:   104 Y:   231 Z:   -13[0m
[0;32mI (2371) DUAL_ADXL345:   [1] X:   104 Y:   228 Z:   -15[0m
[0;32mI (2371) DUAL_ADXL345:   [2] X:    96 Y:   227 Z:    -8[0m
[0;32mI (2381) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2401) DUAL_ADXL345: Sensor 2 data (timestamp: 209, samples: 16):[0m
[0;32mI (2401) DUAL_ADXL345:   [0] X:   102 Y:   235 Z:    -9[0m
[0;32mI (2411) DUAL_ADXL345:   [1] X:   110 Y:   235 Z:    -6[0m
[0;32mI (2411) DUAL_ADXL345:   [2] X:    95 Y:   224 Z:   -21[0m
[0;32mI (2421) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2441) DUAL_ADXL345: Sensor 2 data (timestamp: 213, samples: 16):[0m
[0;32mI (2441) DUAL_ADXL345:   [0] X:   101 Y:   226 Z:   -17[0m
[0;32mI (2451) DUAL_ADXL345:   [1] X:   102 Y:   230 Z:   -17[0m
[0;32mI (2451) DUAL_ADXL345:   [2] X:    99 Y:   228 Z:    -8[0m
[0;32mI (2461) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2481) DUAL_ADXL345: Sensor 2 data (timestamp: 217, samples: 16):[0m
[0;32mI (2481) DUAL_ADXL345:   [0] X:   103 Y:   231 Z:   -11[0m
[0;32mI (2491) DUAL_ADXL345:   [1] X:   103 Y:   231 Z:    -6[0m
[0;32mI (2491) DUAL_ADXL345:   [2] X:    98 Y:   221 Z:   -13[0m
[0;32mI (2501) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2521) DUAL_ADXL345: Sensor 2 data (timestamp: 221, samples: 16):[0m
[0;32mI (2531) DUAL_ADXL345:   [0] X:    98 Y:   229 Z:   -13[0m
[0;32mI (2531) DUAL_ADXL345:   [1] X:   102 Y:   226 Z:   -16[0m
[0;32mI (2531) DUAL_ADXL345:   [2] X:   112 Y:   233 Z:   -18[0m
[0;32mI (2541) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2571) DUAL_ADXL345: Sensor 2 data (timestamp: 225, samples: 16):[0m
[0;32mI (2571) DUAL_ADXL345:   [0] X:   104 Y:   229 Z:   -11[0m
[0;32mI (2571) DUAL_ADXL345:   [1] X:   102 Y:   224 Z:   -12[0m
[0;32mI (2571) DUAL_ADXL345:   [2] X:   100 Y:   227 Z:   -12[0m
[0;32mI (2581) DUAL_ADXL345:   ... (13 more samples)[0m
[0;32mI (2611) DUAL_ADXL345: Sensor 2 data (timestamp: 229, samples: 16):[0m
[0;32mI (2611) DUAL_ADXL345:   [0] X:   101 Y:   236 Z:   -13[0m
[0;32mI (2611) DUAL_ADXL345:   [1] X:   106 Y:   233 Z:    -7[0m
[0;32mI (2611) DUAL_ADXL345:   [2] X:   101 Y:   227 Z:   -17[0m
