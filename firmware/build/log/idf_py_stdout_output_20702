-- Existing sdkconfig '/Users/<USER>/Workspace/master/firmware/sdkconfig' renamed to '/Users/<USER>/Workspace/master/firmware/sdkconfig.old'.
-- Found Git: /usr/bin/git (found version "2.39.5 (Apple Git-154)")
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file /Users/<USER>/Workspace/master/firmware/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: /Users/<USER>/.espressif/python_env/idf5.3_py3.13_env/bin/python (found version "3.13.3") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "firmware" version: 3b2c6ea
-- Adding linker script /Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace /Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock /Users/<USER>/.espressif/esp-idf/v5.3.2/components/console /Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx /Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif_stack /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi /Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py /Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal /Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap /Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser /Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test /Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154 /Users/<USER>/.espressif/esp-idf/v5.3.2/components/json /Users/<USER>/.espressif/esp-idf/v5.3.2/components/log /Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip /Users/<USER>/Workspace/master/firmware/main /Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls /Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt /Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib /Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash /Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider /Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread /Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table /Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon /Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm /Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread /Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc /Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash /Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport /Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element /Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp /Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity /Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb /Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning /Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant /Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa
-- Configuring done (3.1s)
-- Generating done (0.4s)
-- Build files have been written to: /Users/<USER>/Workspace/master/firmware/build
