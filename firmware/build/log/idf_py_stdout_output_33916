ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (30) boot: chip revision: v0.2[0m
[0;32mI (34) boot: efuse block revision: v1.3[0m
[0;32mI (38) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (43) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b704h ( 46852) map[0m
[0;32mI (112) esp_image: segment 1: paddr=0001b72c vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (115) esp_image: segment 2: paddr=0001e1a8 vaddr=40374000 size=01e70h (  7792) load[0m
[0;32mI (122) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a09ch (106652) map[0m
[0;32mI (148) esp_image: segment 4: paddr=0003a0c4 vaddr=40375e70 size=0d970h ( 55664) load[0m
[0;32mI (167) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (167) boot: Disabling RNG early entropy source...[0m
[0;32mI (179) cpu_start: Multicore app[0m
[0;32mI (188) cpu_start: Pro cpu start user code[0m
[0;32mI (188) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (188) app_init: Application information:[0m
[0;32mI (191) app_init: Project name:     firmware[0m
[0;32mI (196) app_init: App version:      3b2c6ea[0m
[0;32mI (201) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (207) app_init: ELF file SHA256:  20c6ed26b...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (217) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (222) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (227) efuse_init: Chip rev:         v0.2[0m
[0;32mI (231) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (239) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (251) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (257) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (264) spi_flash: detected chip: gd[0m
[0;32mI (267) spi_flash: flash io: dio[0m
[0;33mW (271) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (284) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (295) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (302) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (309) main_task: Started on CPU0[0m
[0;32mI (319) main_task: Calling app_main()[0m
[0;31mE (1319) ADXL345: No ADXL345 found (DEVID=0x00)[0m
ESP_ERROR_CHECK failed: esp_err_t 0xffffffff (ESP_FAIL) at 0x42008cde
file: "./main/firmware.c" line 109
func: app_main
expression: adxl345_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc994b0 0x4037acdd:0x3fc994d0 0x40381699:0x3fc994f0 0x4037acd3:0x3fc99560 0x42008cde:0x3fc99590 0x42019777:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 20c6ed26b

Rebooting...
���ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x8 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (31) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (31) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (31) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (38) boot: efuse block revision: v1.3[0m
[0;32mI (43) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (47) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (52) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (57) boot: Enabling RNG early entropy source...[0m
[0;32mI (62) boot: Partition Table:[0m
[0;32mI (66) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (73) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (81) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (88) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (96) boot: End of partition table[0m
[0;32mI (100) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b704h ( 46852) map[0m
[0;32mI (117) esp_image: segment 1: paddr=0001b72c vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (119) esp_image: segment 2: paddr=0001e1a8 vaddr=40374000 size=01e70h (  7792) load[0m
[0;32mI (127) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a09ch (106652) map[0m
[0;32mI (152) esp_image: segment 4: paddr=0003a0c4 vaddr=40375e70 size=0d970h ( 55664) load[0m
[0;32mI (171) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (171) boot: Disabling RNG early entropy source...[0m
[0;32mI (183) cpu_start: Multicore app[0m
[0;32mI (192) cpu_start: Pro cpu start user code[0m
[0;32mI (193) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (193) app_init: Application information:[0m
[0;32mI (195) app_init: Project name:     firmware[0m
[0;32mI (200) app_init: App version:      3b2c6ea[0m
[0;32mI (205) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (211) app_init: ELF file SHA256:  20c6ed26b...[0m
[0;32mI (217) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (226) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (231) efuse_init: Chip rev:         v0.2[0m
[0;32mI (236) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (243) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (249) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (255) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (261) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (269) spi_flash: detected chip: gd[0m
[0;32mI (272) spi_flash: flash io: dio[0m
[0;33mW (276) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (289) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (300) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (307) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (314) main_task: Started on CPU0[0m
[0;32mI (324) main_task: Calling app_main()[0m
[0;31mE (1324) ADXL345: No ADXL345 found (DEVID=0x00)[0m
ESP_ERROR_CHECK failed: esp_err_t 0xffffffff (ESP_FAIL) at 0x42008cde
file: "./main/firmware.c" line 109
func: app_main
expression: adxl345_init()

abort() was called at PC 0x4037acd3 on core 0


Backtrace: 0x40375ade:0x3fc994b0 0x4037acdd:0x3fc994d0 0x40381699:0x3fc994f0 0x4037acd3:0x3fc99560 0x42008cde:0x3fc99590 0x42019777:0x3fc99630 0x4037b785:0x3fc99660




ELF file SHA256: 20c6ed26b

Rebooting...
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x8 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40375a18
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xce8
load:0x403cb700,len:0x2ed8
entry 0x403c8918
[0;32mI (31) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (31) boot: compile time May 27 2025 15:04:03[0m
[0;32mI (31) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (38) boot: efuse block revision: v1.3[0m
[0;32mI (43) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (47) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (52) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (57) boot: Enabling RNG early entropy source...[0m
[0;32mI (62) boot: Partition Table:[0m
[0;32mI (66) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (73) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (81) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (88) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (96) boot: End of partition table[0m
[0;32mI (100) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0b704h ( 46852) map[0m
[0;32mI (117) esp_image: segment 1: paddr=0001b72c vaddr=3fc93800 size=02a74h ( 10868) load[0m
[0;32mI (119) esp_image: segment 2: paddr=0001e1a8 vaddr=40374000 size=01e70h (  7792) load[0m
[0;32mI (127) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1a09ch (106652) map[0m
[0;32mI (152) esp_image: segment 4: paddr=0003a0c4 vaddr=40375e70 size=0d970h ( 55664) load[0m
[0;32mI (171) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (171) boot: Disabling RNG early entropy source...[0m
[0;32mI (183) cpu_start: Multicore app[0m
[0;32mI (192) cpu_start: Pro cpu start user code[0m
[0;32mI (193) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (193) app_init: Application information:[0m
[0;32mI (195) app_init: Project name:     firmware[0m
[0;32mI (200) app_init: App version:      3b2c6ea[0m
[0;32mI (205) app_init: Compile time:     May 27 2025 15:03:58[0m
[0;32mI (211) app_init: ELF file SHA256:  20c6ed26b...[0m
[0;32mI (217) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (226) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (231) efuse_init: Chip rev:         v0.2[0m
[0;32mI (236) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (243) heap_init: At 3FC96B40 len 00052BD0 (330 KiB): RAM[0m
[0;32mI (249) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (255) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (261) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (269) spi_flash: detected chip: gd[0m
[0;32mI (272) spi_flash: flash io: dio[0m
[0;33mW (276) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (289) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (300) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (307) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (314) main_task: Started on CPU0[0m
[0;32mI (324) main_task: Calling app_main()[0m
