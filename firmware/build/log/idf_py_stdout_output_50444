ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x15 (USB_UART_CHIP_RESET),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x4037bad4
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1870
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc40
load:0x403cb700,len:0x2ee8
entry 0x403c8908
[0;32mI (26) boot: ESP-IDF v5.3.2 2nd stage bootloader[0m
[0;32mI (27) boot: compile time May 27 2025 17:01:59[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v0.2[0m
[0;32mI (27) boot: efuse block revision: v1.3[0m
[0;32mI (27) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (28) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (28) boot.esp32s3: SPI Flash Size : 2MB[0m
[0;32mI (28) boot: Enabling RNG early entropy source...[0m
[0;32mI (28) boot: Partition Table:[0m
[0;32mI (28) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (29) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (29) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (30) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (30) boot: End of partition table[0m
[0;32mI (30) esp_image: segment 0: paddr=00010020 vaddr=3c020020 size=0c154h ( 49492) map[0m
[0;32mI (39) esp_image: segment 1: paddr=0001c17c vaddr=3fc93800 size=02a14h ( 10772) load[0m
[0;32mI (42) esp_image: segment 2: paddr=0001eb98 vaddr=40374000 size=01480h (  5248) load[0m
[0;32mI (44) esp_image: segment 3: paddr=00020020 vaddr=42000020 size=1af24h (110372) map[0m
[0;32mI (64) esp_image: segment 4: paddr=0003af4c vaddr=40375480 size=0e344h ( 58180) load[0m
[0;32mI (83) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (83) boot: Disabling RNG early entropy source...[0m
[0;32mI (84) cpu_start: Multicore app[0m
[0;32mI (93) cpu_start: Pro cpu start user code[0m
[0;32mI (93) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (93) app_init: Application information:[0m
[0;32mI (94) app_init: Project name:     firmware[0m
[0;32mI (94) app_init: App version:      a6eb462-dirty[0m
[0;32mI (94) app_init: Compile time:     May 27 2025 17:02:04[0m
[0;32mI (94) app_init: ELF file SHA256:  5fea1025f...[0m
[0;32mI (94) app_init: ESP-IDF:          v5.3.2[0m
[0;32mI (95) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (95) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (95) efuse_init: Chip rev:         v0.2[0m
[0;32mI (95) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (95) heap_init: At 3FC96AD0 len 00052C40 (331 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (96) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (96) heap_init: At 600FE100 len 00001EE8 (7 KiB): RTCRAM[0m
[0;32mI (97) spi_flash: detected chip: gd[0m
[0;32mI (97) spi_flash: flash io: dio[0m
[0;33mW (98) spi_flash: Detected size(8192k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;33mW (98) i2c: This driver is an old driver, please migrate your application code to adapt `driver/i2c_master.h`[0m
[0;32mI (98) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (99) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (100) main_task: Started on CPU0[0m
[0;32mI (110) main_task: Calling app_main()[0m
[0;32mI (110) DUAL_ADXL345: Starting dual ADXL345 sensor firmware[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 0 initialized (SDA=15, SCL=17)[0m
[0;32mI (110) DUAL_ADXL345: I2C Bus 1 initialized (SDA=12, SCL=14)[0m
[0;32mI (210) DUAL_ADXL345: Scanning I2C Bus 0 for I2C devices...[0m
[0;32mI (210) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (220) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 0[0m
[0;32mI (220) DUAL_ADXL345: Scanning I2C Bus 1 for I2C devices...[0m
[0;32mI (230) DUAL_ADXL345:   Device found at address 0x53[0m
[0;32mI (230) DUAL_ADXL345:   Found 1 device(s) on I2C Bus 1[0m
[0;32mI (230) DUAL_ADXL345: UART initialized: 921600 baud, TX=43, RX=44[0m
[0;32mI (230) DUAL_ADXL345: Starting data processing task[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor1_Bus0[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor1_Bus0 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: Starting sensor task for Sensor2_Bus1[0m
[0;32mI (230) DUAL_ADXL345: Sensor1_Bus0 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (230) DUAL_ADXL345: ADXL345 detected on Sensor2_Bus1 (DEVID=0xE5)[0m
[0;32mI (230) DUAL_ADXL345: All tasks created successfully[0m
[0;32mI (240) DUAL_ADXL345: Sensor2_Bus1 initialized: ±16g, FIFO stream @800Hz, burst=16[0m
[0;32mI (240) DUAL_ADXL345: Sensor 1: I2C Bus 0 (SDA=15, SCL=17), Address=0x53[0m
[0;32mI (240) DUAL_ADXL345: Sensor 2: I2C Bus 1 (SDA=12, SCL=14), Address=0x53[0m
[0;32mI (1110) DUAL_ADXL345: Samples per second: 1424/s[0m
[0;32mI (2120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (3130) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (4150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (5160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (6170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (7180) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (8190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (9200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (10210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (10240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (11230) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (12240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (13250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (14260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (15270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (16280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (17290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (18300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (19320) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (20240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (20330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (21340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (22350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (23370) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (24380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (25390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (26400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (27410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (28420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (29430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (30240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (30450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (31460) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (32470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (33480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (34490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (35500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (36520) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (37530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (38540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (39550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (40240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (40560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (41570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (42580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (43590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (44610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (45620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (46630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (47640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (48650) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (49670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (50240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (50680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (51690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (52700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (53710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (54720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (55740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (56750) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (57760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (58770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (59780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (60240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (60790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (61800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (62810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (63820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (64830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (65850) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (66860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (67870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (68880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (69900) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (70240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (70910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (71920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (72930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (73940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (74950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (75970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (76980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (77990) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (79000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (80010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (80240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (81020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (82040) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (83050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (84060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (85070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (86080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (87090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (88100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (89110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (90120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (90240) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (91140) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (92150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (93160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (94170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (95190) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (96200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (97210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (98220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (99230) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (100250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (100250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (101260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (102270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (103280) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (104290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (105300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (106310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (107320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (108340) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (109350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (110250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (110360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (111370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (112380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (113390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (114400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (115420) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (116430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (117440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (118450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (119470) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (120250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (120480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (121490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (122500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (123510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (124520) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (125530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (126550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (127560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (128570) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (129580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (130250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (130590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (131600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (132610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (133620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (134630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (135640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (136660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (137670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (138680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (139690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (140250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (140710) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (141720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (142730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (143740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (144750) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (145760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (146780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (147790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (148800) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (149810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (150250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (150820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (151830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (152840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (153860) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (154870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (155880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (156890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (157900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (158910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (159920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (160250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (160940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (161950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (162960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (163970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (164980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (165990) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (167010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (168020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (169030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (170040) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (170250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (171050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (172060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (173070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (174090) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (175100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (176110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (177120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (178130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (179140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (180150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (180250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (181160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (182180) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (183190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (184200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (185210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (186220) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (187240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (188250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (189260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (190250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (190270) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (191280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (192290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (193300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (194320) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (195330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (196340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (197350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (198360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (199370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (200250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (200380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (201390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (202410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (203420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (204430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (205440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (206450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (207470) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (208480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (209490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (210250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (210500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (211510) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (212520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (213530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (214550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (215560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (216570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (217580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (218590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (219600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (220250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (220610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (221620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (222630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (223650) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (224660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (225670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (226680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (227700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (228710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (229720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (230250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (230730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (231740) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (232750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (233760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (234780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (235790) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (236800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (237810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (238820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (239830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (240250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (240840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (241850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (242860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (243870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (244890) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (245900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (246910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (247920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (248940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (249950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (250250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (250960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (251970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (252980) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (254000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (255010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (256020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (257030) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (258040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (259050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (260070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (260250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (261080) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (262090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (263100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (264110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (265120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (266130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (267140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (268150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (269170) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (270180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (270250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (271190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (272200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (273210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (274230) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (275240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (276250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (277260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (278270) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (279290) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (280250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (280300) DUAL_ADXL345: Samples per second: 1584/s[0m
[0;32mI (281310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (282320) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (283330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (284340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (285350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (286370) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (287380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (288390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (289400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (290250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (290410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (291420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (292430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (293440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (294460) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (295470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (296480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (297490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (298510) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (299520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (300250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (300530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (301540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (302550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (303560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (304580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (305590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (306600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (307610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (308620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (309630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (310250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (310640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (311660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (312670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (313680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (314690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (315700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (316710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (317720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (318730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (319740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (320250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (320760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (321770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (322780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (323790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (324810) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (325820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (326830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (327840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (328850) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (329870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (330250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (330880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (331890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (332900) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (333910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (334920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (335930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (336950) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (337960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (338970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (339980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (340250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (340990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (342000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (343010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (344020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (345030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (346050) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (347060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (348070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (349080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (350100) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (350250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (351110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (352120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (353130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (354140) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (355150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (356160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (357180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (358190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (359200) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (360210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (360250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (361220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (362230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (363250) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (364260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (365270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (366280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (367290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (368300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (369310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (370250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (370320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (371330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (372350) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (373360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (374370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (375380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (376400) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (377410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (378420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (379430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (380250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (380440) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (381460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (382470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (383480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (384490) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (385500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (386510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (387530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (388540) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (389550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (390250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (390560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (391570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (392580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (393600) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (394610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (395620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (396630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (397640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (398650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (399660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (400250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (400670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (401680) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (401690) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (401690) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;33mW (402370) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 4[0m
[0;33mW (402380) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (402700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;33mW (403370) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (403710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (404380) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 9[0m
[0;33mW (404380) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (404720) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;33mW (405370) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 8[0m
[0;33mW (405380) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (405730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (406380) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (406390) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (406740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (407750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (408770) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (409780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (410250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (410790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (411800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (412820) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (413830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (414840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (415850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (416860) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (417880) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (418890) DUAL_ADXL345: Samples per second: 1584/s[0m
[0;32mI (419900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (420250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (420910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (421920) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (422930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (423950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (424960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (425970) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (426980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (427990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (429000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (430020) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (430250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (431030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (432040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (433050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (434060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (435070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (436080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (436480) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 10[0m
[0;33mW (436480) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (436490) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 2[0m
[0;33mW (436490) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (437100) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;33mW (437180) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 6[0m
[0;33mW (437180) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (437190) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0[0m
[0;32mI (438110) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;33mW (438180) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 15[0m
[0;33mW (438180) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (439120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (439180) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 5[0m
[0;33mW (439180) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (439190) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 2[0m
[0;33mW (439190) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (440130) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;33mW (440180) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (440190) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (440200) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (440250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (441150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (442160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (443170) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (444180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (445190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (446200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (447210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (448220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (449230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (450240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (450250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (451260) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (452270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (453280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (454290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (455300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (456320) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (457330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (458340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (459350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (460250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (460370) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (461380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (462390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (463400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (464410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (465430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (466440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (467450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (468460) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (469470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (470250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (470480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (471500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (472510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (473520) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (474530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (475540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (476550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (477560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (478570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (479580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (480250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (480590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (481610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (482620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (483630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (484640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (485650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (486670) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (487680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (488690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (489700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (490250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (490720) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (491730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (492740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (493750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (494760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (495770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (496790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (497800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (498810) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (499820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (500250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (500830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (501850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (502860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (503870) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (504880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (505890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (506900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (507910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (508920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (509930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (510250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (510940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (511960) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (512970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (513980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (514990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (516010) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (517020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (518030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (519040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (520050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (520250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (521070) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (522080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (523090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (524100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (525110) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (526120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (527140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (528150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (529160) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (530170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (530250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (531180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (532190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (533210) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (534220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (535230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (536240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (537250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (538260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (539270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (540250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (540280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (541290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (542310) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (543320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (544330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (545340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (546360) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (547370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (548380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (549390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (550250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (550400) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (551420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (552430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (553440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (554450) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (555460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (556470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (557490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (558500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (559510) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (560250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (560520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (561530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (562540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (563560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (564570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (565580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (566590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (567600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (568610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (569620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (570250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (570630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (571650) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (572660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (573670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (574680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (575700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (576710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (577720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (578730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (579740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (580250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (580750) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (581770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (582780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (583790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (584800) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (585810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (586820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (587840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (588850) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (589860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (590250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (590870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (591880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (592890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (593910) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (594920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (595930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (596940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (597950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (598960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (599970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (600250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (600980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (602000) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (603010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (604020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (605030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (606050) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (607060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (608070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (609080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (610090) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (610250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (611100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (612120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (613130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (614140) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (615150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (616160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (617170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (618180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (619200) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (620210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (620250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (621220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (621380) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 4[0m
[0;33mW (621380) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (621390) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 1[0m
[0;33mW (621390) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (622230) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;32mI (623240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (624250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (625260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (626280) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (627290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (628300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (629310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (630250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (630320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (631330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (632340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (633360) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (634370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (635380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (636390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (637400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (638420) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (639430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (640250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (640440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (641450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (642470) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (643480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (644490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (645500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (646510) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (647520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (648530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (649550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (650250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (650560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (651570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (652580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (653590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (654600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (655620) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (656630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (657640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (658650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (659660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (660250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (660670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (661680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (662700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (663710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (664720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (665730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (666740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (667760) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (668770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (669780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (670250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (670790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (671800) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (672810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (673820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (674840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (675850) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (676860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (677870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (678880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (679890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (680250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (680900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (681910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (682920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (683930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (684950) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (685960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (686970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (687980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (689000) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (690010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (690250) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (691020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (692030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (693040) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (694050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (695070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (696080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (697090) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (698100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (699110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (700120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (700260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (701130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (702140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (703150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (704160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (705180) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (706190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (707200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (708210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (709220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (710240) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (710260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (711250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (712260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (713270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (714280) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (715300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (716310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (717320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (718330) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (719340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (720260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (720350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (721360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (722380) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (723390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (724400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (725410) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (726420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (727430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (728440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (729450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (730260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (730470) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (731480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (732490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (733500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (734510) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (735520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (736540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (737550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (738560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (739570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (740260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (740580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (741590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (742600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (743620) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (744630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (745640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (746650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (747660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (748670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (749680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (750260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (750690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (751710) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (752720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (753730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (754740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (755750) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (756770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (757780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (758790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (759800) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (760260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (760810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (761820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (762830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (763840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (764850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (765860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (766870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (767890) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (768900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (769910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (770260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (770920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (771940) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (772950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (773960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (774970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (775980) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (776990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (778010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (779020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (780030) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (780260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (781040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (782050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (783060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (784070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (785090) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (786100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (787110) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (788120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (789130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (790140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (790260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (791150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (792170) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (793180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (794190) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (795200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (796210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (797220) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;33mW (797640) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (797650) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;32mI (798230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (799240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (800260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (800260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (801270) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (802280) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (803290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (804300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (805310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (806320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (807330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;33mW (807910) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 1[0m
[0;33mW (807910) DUAL_ADXL345: Sensor2_Bus1: Failed to read FIFO status[0m
[0;33mW (807920) DUAL_ADXL345: Sensor2_Bus1: Failed to read sample 0[0m
[0;32mI (808350) DUAL_ADXL345: Samples per second: 1632/s[0m
[0;32mI (809360) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (810260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (810370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (811380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (812390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (813410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (814420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (815430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (816440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (817450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (818460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (819470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (820260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (820480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (821500) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (822510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (823520) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (824530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (825540) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (826550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (827570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (828580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (829590) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (830260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (830600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (831610) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (832620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (833630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (834640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (835650) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (836660) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (837680) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (838690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (839700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (840260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (840710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (841730) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (842740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (843750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (844760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (845770) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (846780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (847800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (848810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (849820) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (850260) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (850830) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (851840) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (852850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (853860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (854870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (855880) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (856890) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (857910) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (858920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (859930) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (860270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (860940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (861960) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (862970) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (863980) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (864990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (866000) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (867010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (868020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (869040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (870050) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (870270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (871060) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (872070) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (873080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (874090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (875110) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (876120) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (877130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (878140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (879150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (880160) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (880270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (881170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (882190) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (883200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (884210) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (885220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (886230) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (887240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (888250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (889270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (890270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (890280) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (891290) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (892300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (893310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (894320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (895330) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (896340) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (897350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (898370) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (899380) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (900270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (900390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (901400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (902410) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (903430) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (904440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (905450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (906460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (907470) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (908480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (909500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (910270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (910510) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (911520) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (912530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (913540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (914550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (915560) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (916570) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (917580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (918590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (919610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (920270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (920620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (921630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (922640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (923660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (924670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (925680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (926690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (927700) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (928710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (929730) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (930270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (930740) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (931750) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (932760) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (933770) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (934780) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (935790) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (936800) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (937810) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (938820) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (939840) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (940270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (940850) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (941860) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (942870) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (943890) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (944900) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (945910) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (946920) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (947930) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (948940) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (949950) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (950270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (950960) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (951980) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (952990) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (954000) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (955010) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (956020) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (957030) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (958040) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (959050) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (960070) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (960270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (961080) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (962090) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (963100) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (964120) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (965130) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (966140) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (967150) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (968160) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (969170) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (970180) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (970270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (971200) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (972210) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (973220) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (974230) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (975240) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (976250) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (977260) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (978270) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (979290) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (980270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (980300) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (981310) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (982320) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (983330) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (984350) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (985360) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (986370) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (987380) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (988390) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (989400) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (990270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (990420) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (991430) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (992440) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (993450) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (994460) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (995470) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (996480) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (997490) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (998500) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (999520) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (1000270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (1000530) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1001540) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1002550) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1003560) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (1004580) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1005590) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1006600) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1007610) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (1008620) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1009630) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1010270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (1010640) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1011660) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (1012670) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1013680) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1014690) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1015700) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1016710) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1017720) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1018740) DUAL_ADXL345: Samples per second: 1616/s[0m
[0;32mI (1019750) DUAL_ADXL345: Samples per second: 1600/s[0m
[0;32mI (1020270) DUAL_ADXL345: Main task heartbeat - system running[0m
[0;32mI (1020760) DUAL_ADXL345: Samples per second: 1600/s[0m
