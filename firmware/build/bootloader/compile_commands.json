[{"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -o CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj -c /Users/<USER>/Workspace/master/firmware/build/bootloader/project_elf_src_esp32s3.c", "file": "/Users/<USER>/Workspace/master/firmware/build/bootloader/project_elf_src_esp32s3.c", "output": "CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/eri.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/eri.c", "output": "esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xt_trax.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xt_trax.c", "output": "esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/lldesc.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/lldesc.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/dport_access_common.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/dport_access_common.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/interrupts.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/interrupts.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gpio_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gpio_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/uart_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/uart_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/adc_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/adc_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/dedic_gpio_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/dedic_gpio_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gdma_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gdma_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/spi_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/spi_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/ledc_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/ledc_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/pcnt_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/pcnt_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rmt_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rmt_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdm_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdm_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2s_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2s_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2c_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2c_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/temperature_sensor_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/temperature_sensor_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/timer_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/timer_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/lcd_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/lcd_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mcpwm_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mcpwm_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mpi_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mpi_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdmmc_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdmmc_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/touch_sensor_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/touch_sensor_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/twai_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/twai_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/wdt_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/wdt_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_dwc_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_dwc_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rtc_io_periph.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rtc_io_periph.c", "output": "esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c", "output": "esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/hal_utils.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/hal_utils.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mpu_hal.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mpu_hal.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/efuse_hal.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/efuse_hal.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/efuse_hal.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/efuse_hal.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mmu_hal.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mmu_hal.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/cache_hal.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/cache_hal.c", "output": "esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include/spi_flash -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_wrap.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_wrap.c", "output": "esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/esp_bootloader_desc.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/esp_bootloader_desc.c", "output": "esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common_loader.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common_loader.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_clock_init.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_clock_init.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_mem.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_mem.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_efuse.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_efuse.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_encrypt.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_encrypt.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/secure_boot.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/secure_boot.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random_esp32s3.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random_esp32s3.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_utility.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_utility.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_partitions.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_partitions.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp_image_format.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp_image_format.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_init.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_init.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_clock_loader.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_clock_loader.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_console.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_console.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_console_loader.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_console_loader.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_sha.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_sha.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_soc.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_soc.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_esp32s3.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/bootloader_esp32s3.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_panic.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_panic.c", "output": "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_table.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_table.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_fields.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_fields.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_utility.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_utility.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_api.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_api.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_fields.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_fields.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_utility.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_utility.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "output": "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c", "output": "esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/cpu.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/cpu.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_memory_utils.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_memory_utils.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_init.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_init.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_time.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_time.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/esp_private -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/chip_info.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/chip_info.c", "output": "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/src/esp_err_to_name.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/src/esp_err_to_name.c", "output": "esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_crc.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_crc.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_sys.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_sys.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_uart.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_uart.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_spiflash.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_spiflash.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_efuse.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_efuse.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_longjmp.S", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_longjmp.S", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_systimer.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_systimer.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_wdt.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_wdt.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S", "output": "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log.c", "output": "esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_buffers.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_buffers.c", "output": "esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/platform_port/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_noos.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_noos.c", "output": "esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj"}, {"directory": "/Users/<USER>/Workspace/master/firmware/build/bootloader", "command": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -I/Users/<USER>/Workspace/master/firmware/build/bootloader/config -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/include/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/include/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/ldo/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/. -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/platform_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/deprecated_include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3 -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/.espressif/esp-idf/v5.3.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj -c /Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/main/bootloader_start.c", "file": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/subproject/main/bootloader_start.c", "output": "esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj"}]