{"COMPONENT_KCONFIGS": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sdkconfig.rename.esp32s3;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/sdkconfig.rename.esp32s3;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/sdkconfig.rename;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/sdkconfig.rename.esp32s3;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.2", "IDF_ENV_FPGA": "", "IDF_PATH": "/Users/<USER>/.espressif/esp-idf/v5.3.2", "COMPONENT_KCONFIGS_SOURCE_FILE": "/Users/<USER>/Workspace/master/firmware/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "/Users/<USER>/Workspace/master/firmware/build/bootloader/kconfigs_projbuild.in"}