{"version": "1.1", "project_name": "firmware", "project_version": "a6eb462-dirty", "project_path": "/Users/<USER>/Workspace/master/firmware", "idf_path": "/Users/<USER>/.espressif/esp-idf/v5.3.2", "build_dir": "/Users/<USER>/Workspace/master/firmware/build", "config_file": "/Users/<USER>/Workspace/master/firmware/sdkconfig", "config_defaults": "", "bootloader_elf": "/Users/<USER>/Workspace/master/firmware/build/bootloader/bootloader.elf", "app_elf": "firmware.elf", "app_bin": "firmware.bin", "build_type": "flash_app", "git_revision": "v5.3.2", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "config_environment": {"COMPONENT_KCONFIGS": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/Kconfig;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py/Kconfig.projbuild;/Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "touch_element", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif_stack", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip", "/Users/<USER>/Workspace/master/firmware/main", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/app_trace/libapp_trace.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/app_trace.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/app_trace_util.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/host_file_io.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/app_update/libapp_update.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update/esp_ota_ops.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_common_loader.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_clock_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_mem.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_efuse.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_encrypt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/secure_boot.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_random_esp32s3.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/bootloader_utility.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/flash_partitions.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp_image_format.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/idf/bootloader_sha.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/cmock/libcmock.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/console/libconsole.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/commands.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/esp_console_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/split_argv.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/linenoise/linenoise.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/esp_console_repl_chip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_cmd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_date.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_dbl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_dstr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_end.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_file.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_hashtable.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_int.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_lit.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_rem.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_rex.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_str.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/arg_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console/argtable3/argtable3.c"], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/cxx/libcxx.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx/cxx_exception_stubs.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx/cxx_guards.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/driver/libdriver.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/adc_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/adc_dma_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/timer_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/i2c/i2c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/i2s_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/mcpwm_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/pcnt_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/rmt_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/sigma_delta_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/deprecated/rtc_temperature_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/touch_sensor/touch_sensor_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/touch_sensor/esp32s3/touch_sensor.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/efuse/libefuse.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_table.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_fields.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/esp32s3/esp_efuse_utility.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_api.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_fields.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_utility.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp_tls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp_tls_error_capture.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp_tls_platform_port.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_oneshot.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_cali.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_cali_curve_fitting.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_continuous.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_monitor.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/gdma/adc_dma.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/adc_filter.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex/esp32s3/esp_coex_adapter.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex/src/coexist_debug_diagram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_common/libesp_common.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam/esp_cam_ctlr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/src/gpio.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/src/rtc_io.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/src/dedic_gpio.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer/src/gptimer.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c/i2c_master.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c/i2c_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/i2s_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/i2s_platform.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/i2s_std.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/i2s_pdm.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s/i2s_tdm.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_cap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_com.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_fault.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_gen.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_oper.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_sync.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/src/rmt_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/src/rmt_encoder.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/src/rmt_rx.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi/src/sdspi_crc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi/src/sdspi_host.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/src/gpspi/spi_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/src/gpspi/spi_master.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/src/gpspi/spi_slave.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/src/gpspi/spi_dma.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart/src/uart.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth/src/esp_eth.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_event/libesp_event.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event/default_event_loop.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event/esp_event.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/gdbstub.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/gdbstub_transport.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/packet.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid/src/esp_hidd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid/src/esp_hidh.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/esp_http_client.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/lib/http_auth.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/lib/http_header.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_main.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_parse.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_sess.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_txrx.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_uri.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/httpd_ws.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/cpu.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_memory_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_clk.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/clk_ctrl_os.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/hw_random.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/intr_alloc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/mac_addr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/periph_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/revision.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/rtc_module.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_modem.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_modes.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_console.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_gpio.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_event.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/regi2c_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_gpio_reserve.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sar_periph_ctrl_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/io_mux.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp_clk_tree_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/esp_dma_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/gdma_link.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/spi_share_hw_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/spi_bus_lock.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/adc_share_hw_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/gdma.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/deprecated/gdma_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/esp_async_memcpy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/dma/async_memcpy_gdma.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/systimer.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_hmac.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_ds.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/mspi_timing_tuning.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/mspi_timing_by_mspi_delay.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/sleep_wake_stub.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/esp_clock_output.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/rtc_time.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/chip_info.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_crypto_lock.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/mspi_timing_config.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp32s3/esp_memprot.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/port/esp_memprot_conv.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support/lowpower/cpu_retention/port/esp32s3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_panel_io.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_panel_st7789.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/src/esp_lcd_panel_ops.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl/src/esp_local_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm/esp_mmu_map.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm/port/esp32s3/ext_mem_layout.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm/esp_cache.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/esp_netif_handlers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/esp_netif_objects.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/esp_netif_defaults.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/esp_netif_lwip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/esp_netif_sntp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/netif/wlanif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/netif/ethernetif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition/partition.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/src/phy_override.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/src/lib_printf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/src/phy_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/src/phy_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/esp32s3/phy_init_data.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm/pm_locks.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm/pm_trace.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram", "type": "CONFIG_ONLY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_crc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_sys.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_uart.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_spiflash.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_efuse.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_longjmp.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_systimer.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_wdt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "include/esp32s3", "esp32s3"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_system/libesp_system.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_err.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/crosscore_int.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_ipc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/freertos_hooks.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/int_wdt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/panic.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/esp_system.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/startup.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/startup_funcs.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/system_time.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/stack_check.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/ubsan.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/xt_wdt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/task_wdt/task_wdt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/cpu_start.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/panic_handler.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_system_chip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/image_process.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/brownout.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/esp_ipc_isr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/panic_arch.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/expression_with_stack.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/debug_helpers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/debug_stubs.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/arch/xtensa/trax.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/highint_hdl.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/clk.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/reset_reason.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/system_internal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/cache_err_int.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/esp_timer.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/esp_timer_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/ets_timer_legacy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/system_time.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/esp_timer_impl_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/lib_printf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/mesh_event.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/smartconfig.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/wifi_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/wifi_default.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/wifi_netif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/wifi_default_ap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/esp32s3/esp_adapter.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "wifi_apps/include", "wifi_apps/nan_app/include", "include/local"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_flash.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_uart.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_elf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_binary.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_sha.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/core_dump_crc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/fatfs/libfatfs.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/diskio/diskio.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/diskio/diskio_rawflash.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/diskio/diskio_wl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/src/ff.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/src/ffunicode.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/port/freertos/ffsystem.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/diskio/diskio_sdmmc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/vfs/vfs_fat.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/vfs/vfs_fat_sdmmc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/freertos/libfreertos.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/heap_idf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/app_startup.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/port_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/port_systick.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/list.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/queue.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/tasks.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/timers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/event_groups.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/esp_additions/freertos_compatibility.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/hal/libhal.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/hal_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mpu_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/efuse_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/efuse_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mmu_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/cache_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/color_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_flash_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_flash_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_flash_encrypt_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/clk_tree_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/systimer_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/uart_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/uart_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/gpio_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/rtc_io_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/timer_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/ledc_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/ledc_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/i2c_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/i2c_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/rmt_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/pcnt_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mcpwm_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/twai_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/twai_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/gdma_hal_top.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/gdma_hal_ahb_v1.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/i2s_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/sdm_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/sdmmc_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/adc_hal_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/adc_oneshot_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/adc_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/lcd_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/mpi_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/sha_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/aes_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/brownout_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_slave_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_slave_hal_iram.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_slave_hd_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/spi_flash_hal_gpspi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/hmac_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/ds_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/usb_serial_jtag_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/usb_dwc_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/usb_wrap_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/touch_sensor_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/touch_sensor_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/xt_wdt_hal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/heap/libheap.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/heap_caps_base.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/heap_caps.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/heap_caps_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/multi_heap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/tlsf/tlsf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/port/memory_layout_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": [], "priv_reqs": ["esp_phy", "driver", "esp_timer", "esp_coex", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/json/libjson.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json/cJSON/cJSON.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/log/liblog.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_buffers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log/log_freertos.c"], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/lwip/liblwip.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/apps/sntp/sntp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/api_lib.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/api_msg.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/err.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/if_api.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/netbuf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/netdb.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/netifapi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/sockets.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/api/tcpip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/apps/sntp/sntp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/def.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/dns.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/inet_chksum.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/mem.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/memp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/netif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/pbuf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/raw.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/stats.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/sys.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/tcp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/tcp_in.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/tcp_out.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/timeouts.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/udp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/autoip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/dhcp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/etharp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/icmp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/igmp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/ip4.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/dhcp6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/ethip6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/icmp6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/inet6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/ip6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/mld6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/core/ipv6/nd6.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ethernet.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/bridgeif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/bridgeif_fdb.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/slipif.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/auth.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/ccp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/chap-md5.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/chap-new.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/chap_ms.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/demand.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/eap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/ecp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/eui64.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/fsm.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/ipcp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/lcp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/magic.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/mppe.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/multilink.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/ppp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/pppapi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/pppoe.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/pppos.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/upap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/vj.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/hooks/tcp_isn_default.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/hooks/lwip_default_hooks.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/debug/lwip_debug.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/sockets_ext.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/freertos/sys_arch.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/port/esp32xx/vfs_lwip.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/apps/ping/esp_ping.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/apps/ping/ping.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/apps/ping/ping_sock.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/Users/<USER>/Workspace/master/firmware/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/main/libmain.a", "sources": ["/Users/<USER>/Workspace/master/firmware/main/firmware.c"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "/Users/<USER>/Workspace/master/firmware/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/mqtt/libmqtt.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/mqtt_client.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/newlib/libnewlib.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/abort.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/assert.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/heap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/locks.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/poll.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/pthread.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/random.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/getentropy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/reent_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/newlib_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/syscalls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/termios.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/stdatomic.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/time.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/sysconf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/realpath.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/scandir.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_api.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_cxx_api.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_item_hash_list.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_page.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_pagemanager.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_storage.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_handle_simple.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_handle_locked.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_partition.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_partition_lookup.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_partition_manager.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_types.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_platform.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/perfmon/libperfmon.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon/xtensa_perfmon_access.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon/xtensa_perfmon_apis.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/protocomm/libprotocomm.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/common/protocomm.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c/constants.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c/sec0.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c/sec1.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c/sec2.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/proto-c/session.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/transports/protocomm_console.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/transports/protocomm_httpd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/security/security0.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/security/security1.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/security/security2.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/crypto/srp6a/esp_srp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/pthread/libpthread.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/pthread.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/pthread_cond_var.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/pthread_local_storage.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/pthread_rwlock.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_cmd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_io.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_mmc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sdmmc_sd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/soc/libsoc.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/lldesc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/dport_access_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/interrupts.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gpio_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/uart_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/adc_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/dedic_gpio_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/gdma_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/spi_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/ledc_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/pcnt_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rmt_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdm_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2s_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/i2c_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/temperature_sensor_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/timer_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/lcd_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mcpwm_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/mpi_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/sdmmc_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/touch_sensor_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/twai_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/wdt_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/usb_dwc_periph.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc/esp32s3/rtc_io_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/flash_brownout_hook.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_hpm_enable.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_drivers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_generic.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_issi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_mxic.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_gd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_winbond.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_boya.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_mxic_opi.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_chip_th.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/memspi_host_driver.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/cache_utils.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/flash_mmap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/flash_ops.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_wrap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/esp_flash_api.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/esp_flash_spi_init.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_os_func_app.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/spiffs/libspiffs.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs_api.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs/src/spiffs_cache.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs/src/spiffs_check.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs/src/spiffs_gc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs/src/spiffs_hydrogen.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/spiffs/src/spiffs_nucleus.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/transport.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/transport_ssl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/transport_internal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/transport_socks_proxy.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/touch_element/libtouch_element.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element/touch_element.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element/touch_button.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element/touch_slider.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/unity/libunity.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity/src/unity.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_compat.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_runner.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_utils_freertos.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_utils_cache.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_utils_memory.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/unity_port_esp32.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/usb/libusb.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/hcd_dwc.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/enum.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/hub.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/usb_helpers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/usb_host.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/usb_private.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/usbh.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/vfs/libvfs.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs/vfs.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs/vfs_eventfd.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs/vfs_semihost.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/Partition.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/SPI_Flash.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/WL_Ext_Perf.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/WL_Ext_Safe.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/WL_Flash.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/crc32.cpp", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/wifi_config.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/wifi_scan.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/wifi_ctrl.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/manager.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/handlers.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/scheme_console.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/port/os_xtensa.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/port/eloop.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/ap_config.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/ieee802_1x.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/wpa_auth.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/sta_info.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/ieee802_11.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/ap/comeback_token.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/common/sae.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/common/dragonfly.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/common/wpa_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/bitfield.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/aes-siv.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha256-kdf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/ccmp.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/aes-gcm.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/crypto_ops.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/dh_group5.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/dh_groups.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/ms_funcs.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha256-prf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha1-prf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha384-prf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/md4-internal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/sha1-tprf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/common/ieee802_11_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/chap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_peap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_tls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/mschapv2.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_fast.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/rsn_supp/wpa.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/base64.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/ext_password.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/uuid.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/wpabuf.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/wpa_debug.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/utils/json.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_attr_build.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_attr_parse.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_attr_process.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_dev_attr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/wps/wps_enrollee.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/common/sae_pk.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/rc4.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/des-internal.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/aes-wrap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/aes-unwrap.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/Users/<USER>/Workspace/master/firmware/build/esp-idf/xtensa/libxtensa.a", "sources": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/eri.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xt_trax.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xtensa_context.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xtensa_intr_asm.S", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xtensa_intr.c", "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3", "esp32s3"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "wifi_apps/include", "wifi_apps/nan_app/include", "include/local"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ieee802154", "lib": "__idf_ieee802154", "reqs": [], "priv_reqs": ["esp_phy", "driver", "esp_timer", "esp_coex", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/Users/<USER>/.espressif/esp-idf/v5.3.2/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/Users/<USER>/.espressif/esp-idf/v5.3.2/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/Users/<USER>/Workspace/master/firmware/main", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}}, "debug_prefix_map_gdbinit": ""}