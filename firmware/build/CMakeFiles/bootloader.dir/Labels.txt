# Target labels
 bootloader
# Source files and their labels
/Users/<USER>/Workspace/master/firmware/build/CMakeFiles/bootloader
/Users/<USER>/Workspace/master/firmware/build/CMakeFiles/bootloader.rule
/Users/<USER>/Workspace/master/firmware/build/CMakeFiles/bootloader-complete.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/Users/<USER>/Workspace/master/firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
